package main

import (
	"fmt"
	"log"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/service/i18n"
	"github.com/labstack/echo/v4"
)

// Example demonstrating how to use the new error translation system

func main() {
	// Initialize i18n service
	i18nService, err := i18n.New()
	if err != nil {
		log.Fatalf("Failed to initialize i18n service: %v", err)
	}

	// Create Echo instance
	e := echo.New()

	// Setup error handler with i18n support
	errorHandler := middlewares.NewHttpErrorHandlerWithI18n(i18nService)
	e.HTTPErrorHandler = errorHandler.Handler

	// Add language detection middleware
	e.Use(middlewares.LanguageDetection(i18nService))

	// Example route that returns different types of errors
	e.GET("/user/:id", func(c echo.Context) error {
		userID := c.Param("id")

		switch userID {
		case "invalid":
			// Example 1: Using new translation key approach
			return errors.NewValidationError(
				errors.Controller,
				"invalid user ID",
				errors.KeyUserErrorInvalidId,
				nil,
			)

		case "notfound":
			// Example 2: Using helper function
			return errors.NewNotFoundError(
				errors.Service,
				errors.UserNotFound,
				errors.KeyUserErrorNotFoundById,
				nil,
			)

		case "forbidden":
			// Example 3: Using specific error constructor
			return errors.NewForbiddenError(
				errors.Middleware,
				errors.UserForbidden,
				errors.KeyUserErrorConflict,
				nil,
			)

		case "conflict":
			// Example 4: Using conflict error
			return errors.NewConflictError(
				errors.Repository,
				"user already exists",
				errors.KeyUserErrorConflict,
				nil,
			)

		default:
			// Success case
			return c.JSON(http.StatusOK, map[string]interface{}{
				"id":   userID,
				"name": "John Doe",
			})
		}
	})

	// Example route showing manual translation
	e.GET("/translate/:key", func(c echo.Context) error {
		key := c.Param("key")
		lang := middlewares.GetLanguageFromEchoContext(c)

		translated := i18nService.Translate(lang, key)

		return c.JSON(http.StatusOK, map[string]interface{}{
			"key":         key,
			"language":    lang,
			"translation": translated,
		})
	})

	// Example route showing all supported languages
	e.GET("/languages", func(c echo.Context) error {
		languages := i18nService.GetSupportedLanguages()
		currentLang := middlewares.GetLanguageFromEchoContext(c)

		return c.JSON(http.StatusOK, map[string]interface{}{
			"supported_languages": languages,
			"current_language":    currentLang,
		})
	})

	fmt.Println("Server starting on :8080")
	fmt.Println("Try these endpoints:")
	fmt.Println("  GET /user/invalid    - Returns validation error")
	fmt.Println("  GET /user/notfound   - Returns not found error")
	fmt.Println("  GET /user/forbidden  - Returns forbidden error")
	fmt.Println("  GET /user/conflict   - Returns conflict error")
	fmt.Println("  GET /user/123        - Returns success")
	fmt.Println("  GET /translate/user.error.notFound - Manual translation")
	fmt.Println("  GET /languages       - Show supported languages")
	fmt.Println("")
	fmt.Println("Test with different Accept-Language headers:")
	fmt.Println("  curl -H 'Accept-Language: pt-BR' http://localhost:8080/user/invalid")
	fmt.Println("  curl -H 'Accept-Language: en-US' http://localhost:8080/user/invalid")
	fmt.Println("  curl -H 'Accept-Language: es-ES' http://localhost:8080/user/invalid")

	log.Fatal(e.Start(":8080"))
}
