# App Store Subscription Management

Apple's guidelines are strict: **if you are selling digital content, features, or subscriptions for use *within your app*, you MUST use their In-App Purchase (IAP) system.**

### App Store Server Notifications

The modern and recommended approach is to use **App Store Server Notifications V2**, which sends you a signed JSON Web Token (JWT). This is more secure than the older V1 plaintext JSON notifications.

### The Big Picture: In-App Purchase Flow

1.  **Frontend (iOS App):** Your app uses Apple's `StoreKit` framework to display products and initiate a purchase.
2.  **Apple Processes Payment:** The user authenticates with Face ID/Touch ID. Apple handles the entire payment flow.
3.  **App Receives Transaction:** `StoreKit` on the app receives a successful transaction object, which includes a unique `originalTransactionId`.
4.  **App -> Your Go Backend (Recommended):** Your app sends the `originalTransactionId` to your backend, where you link it to the authenticated user's `UserID`. This step is crucial for identifying which user owns the subscription when the webhook arrives.
5.  **Apple -> Your Go Backend (The Source of Truth):** Asynchronously, Apple sends an **App Store Server Notification** (a signed JWT) to your webhook endpoint. This webhook is your ultimate source of truth for a user's subscription status.
6.  **Your Go Backend:**
    a.  Receives the POST request from Apple.
    b.  **Verifies the JWT signature** using Apple's public keys. This is a critical security step.
    c.  Decodes the JWT payload to get the notification data.
    d.  **Creates or updates the `Subscription` document** in your MongoDB collection based on the `notificationType`.

---

### Step 1: Prerequisites (App Store Connect Setup)

1.  **Create In-App Purchases:** In App Store Connect, under your app, go to "In-App Purchases" and define the products you want to sell (e.g., "Pro Subscription - Monthly").
2.  **Set Up Server Notifications URL:**
    *   Go to `App Store Connect > [Your App] > App Settings > General`.
    *   Scroll down to "App Store Server Notifications".
    *   Enter your production webhook URL (e.g., `https://api.yourdomain.com/webhooks/app-store`).
    *   Choose **Version 2**.
    *   You can also set a URL for the Sandbox environment for testing.
3.  **Generate API Keys:**
    *   Go to `Users and Access > Keys`.
    *   Generate a new API key with "App Manager" access.
    *   Note the **Issuer ID**, the **Key ID**, and **download the private key file (`.p8`)**.

---

### Step 2: Golang Backend Implementation

Let's adapt your code to securely handle V2 notifications and interact with your MongoDB `Subscription` model.

#### Project Setup
You'll need a robust JWT library and the MongoDB Go driver.

```bash
go get github.com/golang-jwt/jwt/v4
go get go.mongodb.org/mongo-driver/mongo
```

#### Your MongoDB `Subscription` Model

The existing Subscription model in the billing system is perfectly suited for this task. We will map Apple's notification data directly to this structure.

```go
// Existing Subscription model from internal/model/billing/subscription.go
type Subscription struct {
	ObjectID primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID       string             `json:"id,omitempty" bson:"-"`

	// Core relationships
	UserID    primitive.ObjectID `json:"userId" bson:"userId" validate:"required"`
	UserEmail string             `json:"userEmail" bson:"userEmail" validate:"required"`
	PlanID    primitive.ObjectID `json:"planId" bson:"planId" validate:"required"`

	// Subscription status and lifecycle
	Status SubscriptionStatus `json:"status" bson:"status" validate:"required"`

	// Payment provider information
	Provider               PaymentProvider `json:"provider" bson:"provider" validate:"required"`
	ProviderSubscriptionID string          `json:"providerSubscriptionId,omitempty" bson:"providerSubscriptionId,omitempty"` // <-- This will store the originalTransactionId

	// Subscription dates
	StartDate    time.Time  `json:"startDate" bson:"startDate" validate:"required"`
	EndDate      time.Time  `json:"endDate" bson:"endDate" validate:"required"`
	TrialEndDate *time.Time `json:"trialEndDate,omitempty" bson:"trialEndDate,omitempty"`

	// Subscription configuration
	AutoRenew bool `json:"autoRenew" bson:"autoRenew"`

	// Cancellation information
	CancelledAt        *time.Time `json:"cancelledAt,omitempty" bson:"cancelledAt,omitempty"`
	CancellationReason string     `json:"cancellationReason,omitempty" bson:"cancellationReason,omitempty"`

	// Metadata
	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"`
}
```

**`ProviderSubscriptionID` is the most important field.** We will store Apple's `originalTransactionId` here. It serves as the unique key to find and update a user's subscription record throughout its entire lifecycle.

#### Required Subscription Status Extensions

The current billing system needs additional subscription statuses for App Store integration:

```go
const (
	SubscriptionStatusActive    SubscriptionStatus = "active"
	SubscriptionStatusTrial     SubscriptionStatus = "trial"
	SubscriptionStatusCancelled SubscriptionStatus = "cancelled"
	SubscriptionStatusExpired   SubscriptionStatus = "expired"
	SubscriptionStatusSuspended SubscriptionStatus = "suspended"
	// New statuses needed for App Store
	SubscriptionStatusPastDue   SubscriptionStatus = "past_due"   // For billing failures
	SubscriptionStatusRevoked   SubscriptionStatus = "revoked"    // For refunds
)
```

#### Payment Provider Update

The current system uses `PaymentProviderApplePay` but should be updated to `PaymentProviderAppStore` for consistency:

```go
const (
	PaymentProviderHotmart   PaymentProvider = "hotmart"
	PaymentProviderAppStore  PaymentProvider = "appstore"  // Updated from "applepay"
)
```

#### The Webhook Controller

Following the existing billing controller pattern, the App Store webhook controller should be implemented as follows:

```go
// internal/controller/billing/controller.go

// Add to Controller interface
type Controller interface {
	// ... existing methods
	AppStoreWebhook() echo.HandlerFunc
}

// Add to route registration in RegisterRoutes
func (bc *controller) RegisterRoutes(e *echo.Echo) {
	// ... existing routes

	// Webhook endpoints (no authentication required)
	webhooksGroup := billingGroup.Group("/webhooks")
	webhooksGroup.POST("/hotmart", bc.HotmartWebhook())
	webhooksGroup.POST("/appstore", bc.AppStoreWebhook()) // Updated from "/applepay"
}

// Webhook controller implementation
func (bc *controller) AppStoreWebhook() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Read raw body for JWT verification
		body, err := io.ReadAll(c.Request().Body)
		if err != nil {
			return errors.New(errors.Controller, "failed to read request body", errors.Validation, err)
		}

		if len(body) == 0 {
			return errors.New(errors.Controller, "empty request body", errors.Validation, nil)
		}

		// Parse the signed payload from Apple's request
		var payload map[string]interface{}
		if err := json.Unmarshal(body, &payload); err != nil {
			return errors.New(errors.Controller, "invalid JSON payload", errors.Validation, err)
		}

		signedPayload, ok := payload["signedPayload"].(string)
		if !ok {
			return errors.New(errors.Controller, "missing signedPayload in request", errors.Validation, nil)
		}

		// Process the webhook through the service
		if err := bc.Service.ProcessAppStoreWebhook(ctx, signedPayload); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, MessageResponse{Message: "webhook processed successfully"})
	}
}
```

#### The Webhook Service (The Core Logic)

Following the existing service interface pattern, we need to add App Store webhook methods to the WebhookService interface:

```go
// internal/service/billing/service.go

// Add to WebhookService interface
type WebhookService interface {
	// Hotmart webhook handlers
	ProcessHotmartWebhook(ctx context.Context, webhookData interface{}) error
	HandleHotmartSubscriptionCreated(ctx context.Context, webhookData hotmart.PurchaseData) error
	HandleHotmartSubscriptionCancelled(ctx context.Context, webhookData interface{}) error
	HandleHotmartPaymentCompleted(ctx context.Context, webhookData interface{}) error
	HandleHotmartPaymentRefunded(ctx context.Context, webhookData interface{}) error

	// App Store webhook handlers
	ProcessAppStoreWebhook(ctx context.Context, signedPayload string) error
	HandleAppStoreSubscriptionCreated(ctx context.Context, webhookData interface{}) error
	HandleAppStoreSubscriptionCancelled(ctx context.Context, webhookData interface{}) error
	HandleAppStorePaymentCompleted(ctx context.Context, webhookData interface{}) error
	HandleAppStorePaymentRefunded(ctx context.Context, webhookData interface{}) error
}
```

#### App Store Webhook Data Structures

```go
// internal/service/billing/webhook.go

import (
	"context"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
)

// AppStoreNotification represents the decoded JWT payload from Apple
type AppStoreNotification struct {
	NotificationType string          `json:"notificationType"`
	Subtype          string          `json:"subtype"`
	NotificationUUID string          `json:"notificationUUID"`
	Data             TransactionInfo `json:"data"`
	Version          string          `json:"version"`
	SignedDate       int64           `json:"signedDate"`
	Environment      string          `json:"environment"`
}

type TransactionInfo struct {
	AppAppleID            int64  `json:"appAppleId"`
	BundleID              string `json:"bundleId"`
	BundleVersion         string `json:"bundleVersion"`
	Environment           string `json:"environment"`
	OriginalTransactionID string `json:"originalTransactionId"`
	ProductID             string `json:"productId"`
	PurchaseDate          int64  `json:"purchaseDate"`
	ExpiresDate           int64  `json:"expiresDate"`
	TransactionID         string `json:"transactionId"`
	Type                  string `json:"type"` // "Auto-Renewable Subscription"
	// Additional fields can be added as needed
}

// ProcessAppStoreWebhook is the main webhook processor following the existing pattern
func (s *service) ProcessAppStoreWebhook(ctx context.Context, signedPayload string) error {
	// 1. Decode and Verify the JWT
	notification, err := s.verifyAndDecodeJWT(signedPayload)
	if err != nil {
		return errors.New(errors.Service, "JWT verification failed", errors.Validation, err)
	}

	// 2. Log webhook received (following existing logging pattern)
	log.Infoj(log.JSON{
		"message":                "Processing App Store webhook",
		"provider":               "appstore",
		"notification_type":      notification.NotificationType,
		"notification_uuid":      notification.NotificationUUID,
		"original_transaction_id": notification.Data.OriginalTransactionID,
		"environment":            notification.Environment,
	})

	// 3. TODO: Implement idempotency check using notification.NotificationUUID
	// This should check if we've already processed this notification to prevent duplicates

	// 4. Route to appropriate handler based on the V2 notification type
	switch notification.NotificationType {
	case "SUBSCRIBED", "DID_RENEW":
		// Both result in an active subscription - combine into single handler
		return s.HandleAppStoreSubscriptionCreated(ctx, *notification)

	case "DID_FAIL_TO_RENEW":
		// Billing failure - put subscription in grace period
		return s.HandleAppStoreBillingError(ctx, *notification)

	case "CANCEL":
		// Voluntary cancellation by user
		return s.HandleAppStoreSubscriptionCancelled(ctx, *notification)

	case "REFUND":
		// Payment refunded - revoke access immediately
		return s.HandleAppStorePaymentRefunded(ctx, *notification)

	case "EXPIRED":
		// Subscription officially ended
		return s.HandleAppStoreSubscriptionExpired(ctx, *notification)

	default:
		// Log unknown notification type but don't fail (following existing pattern)
		log.Printf("Unhandled App Store notification type: %s", notification.NotificationType)
		return nil
	}
}

// verifyAndDecodeJWT is the critical security function for validating Apple's JWT
func (s *service) verifyAndDecodeJWT(tokenString string) (*AppStoreNotification, error) {
	// Note: Apple's public key fetching should be cached in production
	keyFunc := func(token *jwt.Token) (interface{}, error) {
		// Ensure the signing method is what we expect
		if _, ok := token.Method.(*jwt.SigningMethodECDSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// Get the x5c header, which contains the certificate chain
		x5c, ok := token.Header["x5c"].([]interface{})
		if !ok || len(x5c) == 0 {
			return nil, errors.New(errors.Service, "x5c header missing or empty", errors.Validation, nil)
		}

		// The first certificate is the signing cert
		certStr, ok := x5c[0].(string)
		if !ok {
			return nil, errors.New(errors.Service, "invalid x5c certificate format", errors.Validation, nil)
		}

		certBytes, err := base64.StdEncoding.DecodeString(certStr)
		if err != nil {
			return nil, errors.New(errors.Service, "failed to decode certificate", errors.Validation, err)
		}

		cert, err := x509.ParseCertificate(certBytes)
		if err != nil {
			return nil, errors.New(errors.Service, "failed to parse certificate", errors.Validation, err)
		}

		// TODO: In production, you MUST also verify the certificate chain here
		// to ensure it's signed by Apple's root CA.
		// This involves checking the certificate chain against Apple's known root certificates.

		return cert.PublicKey, nil
	}

	token, err := jwt.Parse(tokenString, keyFunc)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to parse JWT", errors.Validation, err)
	}

	if !token.Valid {
		return nil, errors.New(errors.Service, "JWT token is not valid", errors.Validation, nil)
	}

	// Unmarshal the payload into our struct
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New(errors.Service, "failed to get JWT claims", errors.Internal, nil)
	}

	payloadBytes, err := json.Marshal(claims)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to marshal claims", errors.Internal, err)
	}

	var notification AppStoreNotification
	if err := json.Unmarshal(payloadBytes, &notification); err != nil {
		return nil, errors.New(errors.Service, "failed to unmarshal claims into notification struct", errors.Internal, err)
	}

	return &notification, nil
}


// HandleAppStoreSubscriptionCreated handles both new subscriptions (SUBSCRIBED) and renewals (DID_RENEW)
// Following the existing service pattern for subscription creation
func (s *service) HandleAppStoreSubscriptionCreated(ctx context.Context, notification AppStoreNotification) error {
	log.Infoj(log.JSON{
		"message":                "Processing App Store subscription creation/renewal",
		"provider":               "appstore",
		"notification_type":      notification.NotificationType,
		"original_transaction_id": notification.Data.OriginalTransactionID,
		"product_id":             notification.Data.ProductID,
	})

	// First, try to find an existing subscription
	sub, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderAppStore, notification.Data.OriginalTransactionID)

	if err != nil {
		// If it's a "not found" error, this is a new subscription (SUBSCRIBED)
		if errors.IsNotFound(err) {
			// CRITICAL: User identification problem
			// Your iOS app must have previously sent the originalTransactionId to your backend
			// and linked it to the authenticated user. This function looks up that link.
			user, userErr := s.findUserForTransaction(ctx, notification.Data.OriginalTransactionID)
			if userErr != nil {
				return errors.New(errors.Service, "could not link transaction to user", errors.Internal, userErr)
			}

			// Find the plan by Apple's product ID
			plan, planErr := s.findPlanByAppleProductID(ctx, notification.Data.ProductID)
			if planErr != nil {
				return errors.New(errors.Service, "plan not found for Apple product ID", errors.NotFound, planErr)
			}

			// Create a new subscription using the existing service method pattern
			userID, err := primitive.ObjectIDFromHex(user.ID)
			if err != nil {
				return errors.New(errors.Service, "invalid user ID", errors.Internal, err)
			}

			subscription, err := s.CreateSubscription(ctx, userID, plan.ObjectID, billing.PaymentProviderAppStore)
			if err != nil {
				return err
			}

			// Update with App Store specific data
			subscription.ProviderSubscriptionID = notification.Data.OriginalTransactionID
			subscription.StartDate = time.UnixMilli(notification.Data.PurchaseDate)
			subscription.EndDate = time.UnixMilli(notification.Data.ExpiresDate)
			subscription.Status = billing.SubscriptionStatusActive
			subscription.AutoRenew = true

			// Update subscription first
			if err := s.repo.Subscriptions().Update(ctx, subscription); err != nil {
				return err
			}

			// Create payment record for this transaction
			// Note: Apple doesn't provide exact amount in webhooks, so we use plan price
			payment, err := s.ProcessPayment(ctx, userID, &subscription.ObjectID, billing.PaymentProviderAppStore, notification.Data.TransactionID, int64(plan.Price), plan.Currency, notification)
			if err != nil {
				return err
			}

			// Mark payment as completed
			return s.MarkPaymentCompleted(ctx, payment.ObjectID)
		}
		// If it's another type of error, return it
		return err
	}

	// If the subscription was found, this is a renewal (DID_RENEW). Update it.
	sub.EndDate = time.UnixMilli(notification.Data.ExpiresDate)
	sub.Status = billing.SubscriptionStatusActive
	sub.AutoRenew = true // Ensure auto-renew is enabled for successful renewals

	// Update subscription first
	if err := s.repo.Subscriptions().Update(ctx, sub); err != nil {
		return err
	}

	// Create payment record for the renewal
	plan, err := s.repo.Plans().Find(ctx, sub.PlanID)
	if err != nil {
		return err
	}

	payment, err := s.ProcessPayment(ctx, sub.UserID, &sub.ObjectID, billing.PaymentProviderAppStore, notification.Data.TransactionID, int64(plan.Price), plan.Currency, notification)
	if err != nil {
		return err
	}

	// Mark payment as completed
	return s.MarkPaymentCompleted(ctx, payment.ObjectID)
}

// HandleAppStoreBillingError handles DID_FAIL_TO_RENEW, putting the user in a grace period
func (s *service) HandleAppStoreBillingError(ctx context.Context, notification AppStoreNotification) error {
	log.Infoj(log.JSON{
		"message":                "Processing App Store billing error",
		"provider":               "appstore",
		"notification_type":      notification.NotificationType,
		"original_transaction_id": notification.Data.OriginalTransactionID,
	})

	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderAppStore, notification.Data.OriginalTransactionID)
	if err != nil {
		return err
	}

	// Note: SubscriptionStatusPastDue needs to be added to the billing system
	subscription.Status = billing.SubscriptionStatusPastDue
	subscription.AutoRenew = false // Disable auto-renew during billing issues

	return s.repo.Subscriptions().Update(ctx, subscription)
}

// HandleAppStoreSubscriptionCancelled handles the CANCEL notification
// This means the user has disabled auto-renew, but their subscription is still active until EndDate
func (s *service) HandleAppStoreSubscriptionCancelled(ctx context.Context, notification AppStoreNotification) error {
	log.Infoj(log.JSON{
		"message":                "Processing App Store subscription cancellation",
		"provider":               "appstore",
		"notification_type":      notification.NotificationType,
		"original_transaction_id": notification.Data.OriginalTransactionID,
	})

	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderAppStore, notification.Data.OriginalTransactionID)
	if err != nil {
		return err // Subscription should exist
	}

	// Update the subscription to reflect the user's intent
	// We do not revoke access here. Access is valid until the EndDate.
	subscription.AutoRenew = false
	subscription.Status = billing.SubscriptionStatusCancelled
	now := time.Now()
	subscription.CancelledAt = &now
	subscription.CancellationReason = "user_cancelled"

	return s.repo.Subscriptions().Update(ctx, subscription)
}

// HandleAppStorePaymentRefunded handles the REFUND notification, revoking access immediately
func (s *service) HandleAppStorePaymentRefunded(ctx context.Context, notification AppStoreNotification) error {
	log.Infoj(log.JSON{
		"message":                "Processing App Store payment refund",
		"provider":               "appstore",
		"notification_type":      notification.NotificationType,
		"original_transaction_id": notification.Data.OriginalTransactionID,
	})

	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderAppStore, notification.Data.OriginalTransactionID)
	if err != nil {
		if errors.IsNotFound(err) {
			// Log the refund attempt even if subscription is not found
			log.Printf("Received refund for unknown subscription: %s", notification.Data.OriginalTransactionID)
			return nil // Don't fail the webhook for unknown subscriptions
		}
		return err
	}

	// Revoke access immediately - Note: SubscriptionStatusRevoked needs to be added to the billing system
	subscription.Status = billing.SubscriptionStatusRevoked
	subscription.AutoRenew = false
	subscription.CancellationReason = "refund"
	now := time.Now()
	subscription.CancelledAt = &now

	// Also mark any related payment as refunded
	if notification.Data.TransactionID != "" {
		payment, paymentErr := s.repo.Payments().FindByProviderTransactionID(ctx, billing.PaymentProviderAppStore, notification.Data.TransactionID)
		if paymentErr == nil {
			if err := s.MarkPaymentRefunded(ctx, payment.ObjectID); err != nil {
				// Log error but don't fail the subscription update
				log.Printf("Failed to mark payment as refunded: %v", err)
			}
		}
	}

	return s.repo.Subscriptions().Update(ctx, subscription)
}

// HandleAppStoreSubscriptionExpired handles the EXPIRED notification
// This is the final signal that a subscription's access period has ended
func (s *service) HandleAppStoreSubscriptionExpired(ctx context.Context, notification AppStoreNotification) error {
	log.Infoj(log.JSON{
		"message":                "Processing App Store subscription expiration",
		"provider":               "appstore",
		"notification_type":      notification.NotificationType,
		"subtype":                notification.Subtype,
		"original_transaction_id": notification.Data.OriginalTransactionID,
	})

	// Find the subscription by its unique provider ID
	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderAppStore, notification.Data.OriginalTransactionID)
	if err != nil {
		if errors.IsNotFound(err) {
			// This can happen if the subscription was created and expired between webhook checks,
			// or was manually deleted. It's safe to ignore.
			log.Printf("Received EXPIRED notification for a subscription not found in DB: %s", notification.Data.OriginalTransactionID)
			return nil
		}
		// For other errors, we want Apple to retry
		return err
	}

	// Update the subscription to its final, expired state
	subscription.Status = billing.SubscriptionStatusExpired
	subscription.AutoRenew = false

	// The 'subtype' provides valuable analytics on why the subscription expired
	// e.g., "VOLUNTARY" (user cancelled) or "BILLING_RETRY" (retries failed)
	if notification.Subtype != "" {
		subscription.CancellationReason = notification.Subtype
	}

	return s.repo.Subscriptions().Update(ctx, subscription)
}

// Helper functions needed for App Store integration

// findUserForTransaction looks up the user who initiated the transaction
// This requires implementing a mechanism to link originalTransactionId to users
// when they initiate purchases from the iOS app
func (s *service) findUserForTransaction(ctx context.Context, originalTransactionID string) (*model.User, error) {
	// TODO: Implement user-transaction linking mechanism
	// This could be:
	// 1. A separate collection storing originalTransactionId -> userID mappings
	// 2. A field in the user model storing pending transaction IDs
	// 3. A temporary cache/Redis store for linking transactions to users

	// For now, return an error indicating this needs implementation
	return nil, errors.New(errors.Service, "user-transaction linking not implemented", errors.NotImplemented, nil)
}

// findPlanByAppleProductID finds a plan by Apple's product ID
func (s *service) findPlanByAppleProductID(ctx context.Context, appleProductID string) (*billing.Plan, error) {
	// Use the existing plan repository to find by Apple product ID
	plans, err := s.repo.Plans().FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, plan := range plans {
		if plan.AppleProductID == appleProductID {
			return plan, nil
		}
	}

	return nil, errors.New(errors.Service, "plan not found for Apple product ID", errors.NotFound, nil)
}

---

## Payment Processing Integration

### Why Payment Processing is Needed

App Store integration requires payment processing for several reasons:

1. **Financial Auditing**: Track individual transactions for accounting and reconciliation
2. **Refund Handling**: Apple can issue refunds independently, requiring payment record updates
3. **Revenue Analytics**: Analyze payment patterns, renewal rates, and revenue metrics
4. **Compliance**: Maintain detailed payment records for tax and regulatory requirements
5. **Consistency**: Follow the same pattern as existing Hotmart integration

### Payment Processing Flow

For App Store webhooks, the payment processing flow is:

1. **Subscription Creation/Renewal** (`SUBSCRIBED`, `DID_RENEW`):
   - Create/update subscription record
   - Create payment record using `ProcessPayment()`
   - Mark payment as completed using `MarkPaymentCompleted()`

2. **Payment Refunds** (`REFUND`):
   - Find existing payment by transaction ID
   - Mark payment as refunded using `MarkPaymentRefunded()`
   - Update subscription status to revoked

3. **Billing Failures** (`DID_FAIL_TO_RENEW`):
   - Update subscription to past_due status
   - Optionally create failed payment record for tracking

### Using Existing Payment Methods

The billing system already provides all necessary payment processing methods:

```go
// These methods are already implemented and available:
func (s *service) ProcessPayment(ctx context.Context, userID primitive.ObjectID, subscriptionID *primitive.ObjectID, provider billing.PaymentProvider, providerTransactionID string, amount int64, currency string, webhookData interface{}) (*billing.Payment, error)

func (s *service) MarkPaymentCompleted(ctx context.Context, paymentID primitive.ObjectID) error

func (s *service) MarkPaymentRefunded(ctx context.Context, paymentID primitive.ObjectID) error
```

---

## Implementation Requirements

### 1. Update Billing System Constants

Add the missing subscription statuses and update the payment provider:

```go
// internal/model/billing/subscription.go

const (
	SubscriptionStatusActive    SubscriptionStatus = "active"
	SubscriptionStatusTrial     SubscriptionStatus = "trial"
	SubscriptionStatusCancelled SubscriptionStatus = "cancelled"
	SubscriptionStatusExpired   SubscriptionStatus = "expired"
	SubscriptionStatusSuspended SubscriptionStatus = "suspended"
	// Add these new statuses
	SubscriptionStatusPastDue   SubscriptionStatus = "past_due"
	SubscriptionStatusRevoked   SubscriptionStatus = "revoked"
)

const (
	PaymentProviderHotmart   PaymentProvider = "hotmart"
	PaymentProviderAppStore  PaymentProvider = "appstore"  // Update from "applepay"
)

// Update validation functions
func (ss SubscriptionStatus) IsValid() bool {
	switch ss {
	case SubscriptionStatusActive, SubscriptionStatusTrial, SubscriptionStatusCancelled,
		SubscriptionStatusExpired, SubscriptionStatusSuspended, SubscriptionStatusPastDue, SubscriptionStatusRevoked:
		return true
	default:
		return false
	}
}

func (pp PaymentProvider) IsValid() bool {
	switch pp {
	case PaymentProviderHotmart, PaymentProviderAppStore:
		return true
	default:
		return false
	}
}
```

### 2. Update Controller DTO

```go
// internal/controller/billing/dto.go

type CreateSubscriptionRequest struct {
	PlanID   string `json:"planId" validate:"required"`
	Provider string `json:"provider" validate:"required,oneof=hotmart appstore"` // Update validation
}
```

### 3. Add Dependencies

```bash
go get github.com/golang-jwt/jwt/v4
```

### 4. Environment Variables

Add to your environment configuration:

```env
# App Store Configuration
APPLE_BUNDLE_ID=com.yourcompany.yourapp
APPLE_ENVIRONMENT=sandbox  # or "production"
```

### 5. User-Transaction Linking Implementation

You need to implement a mechanism to link Apple's `originalTransactionId` to your users. Here are three approaches:

#### Option A: Transaction Linking Collection
```go
// internal/model/billing/transaction_link.go
type TransactionLink struct {
	ObjectID              primitive.ObjectID `bson:"_id,omitempty"`
	UserID                primitive.ObjectID `bson:"userId"`
	OriginalTransactionID string             `bson:"originalTransactionId"`
	CreatedAt             time.Time          `bson:"createdAt"`
	ExpiresAt             time.Time          `bson:"expiresAt"` // Auto-expire after 24 hours
}
```

#### Option B: Add to User Model
```go
// Add to internal/model/user.go
type User struct {
	// ... existing fields
	PendingTransactionIDs []string `json:"pendingTransactionIds,omitempty" bson:"pendingTransactionIds,omitempty"`
}
```

#### Option C: Redis Cache (Recommended for high volume)
```go
// Use Redis to store temporary mappings
// Key: "apple_transaction:{originalTransactionId}"
// Value: "{userID}"
// TTL: 24 hours
```

---

## Testing

### 1. Webhook Testing with Apple's Sandbox

Apple provides a sandbox environment for testing webhooks:

```go
// internal/service/billing/webhook_test.go
func TestAppStoreWebhookProcessing(t *testing.T) {
	// Mock JWT payload from Apple's sandbox
	mockJWT := createMockAppleJWT(t, AppStoreNotification{
		NotificationType: "SUBSCRIBED",
		Data: TransactionInfo{
			OriginalTransactionID: "test_transaction_123",
			ProductID:            "com.yourapp.premium_monthly",
			PurchaseDate:         time.Now().UnixMilli(),
			ExpiresDate:          time.Now().Add(30*24*time.Hour).UnixMilli(),
		},
	})

	service := setupTestService(t)
	err := service.ProcessAppStoreWebhook(context.Background(), mockJWT)
	assert.NoError(t, err)
}
```

### 2. Integration Testing

```bash
# Test webhook endpoint
curl -X POST http://localhost:8080/v2/billing/webhooks/appstore \
  -H "Content-Type: application/json" \
  -d '{"signedPayload": "your_test_jwt_here"}'
```

---

## Security Considerations

### 1. JWT Signature Verification

**Critical**: Always verify Apple's JWT signature using their public keys:

```go
// Production implementation must include certificate chain validation
func (s *service) validateAppleCertificateChain(cert *x509.Certificate) error {
	// 1. Check certificate is not expired
	now := time.Now()
	if now.Before(cert.NotBefore) || now.After(cert.NotAfter) {
		return errors.New(errors.Service, "certificate is expired or not yet valid", errors.Validation, nil)
	}

	// 2. Verify certificate chain against Apple's root CA
	// TODO: Implement full certificate chain validation
	// This involves checking the certificate chain against Apple's known root certificates

	return nil
}
```

### 2. Idempotency

Implement webhook idempotency using `notificationUUID`:

```go
// Check if notification was already processed
func (s *service) isNotificationProcessed(ctx context.Context, notificationUUID string) (bool, error) {
	// TODO: Implement using a processed_notifications collection or cache
	// Store notificationUUID with TTL of 30 days (Apple's recommendation)
	return false, nil
}
```

### 3. Rate Limiting

Apple may retry failed webhooks, so implement rate limiting:

```go
// Add rate limiting middleware to webhook endpoint
func (bc *controller) AppStoreWebhook() echo.HandlerFunc {
	return middleware.RateLimiter(middleware.NewRateLimiterMemoryStore(20))(
		func(c echo.Context) error {
			// ... webhook implementation
		},
	)
}
```

---

## Implementation Steps

### Phase 1: Core Infrastructure
1. **Update billing system constants** (subscription statuses, payment provider)
2. **Add JWT verification dependencies** (`golang-jwt/jwt/v4`)
3. **Implement basic webhook controller** following existing patterns
4. **Add App Store webhook service methods** to the interface

### Phase 2: User Linking
1. **Choose user-transaction linking approach** (collection, user model, or Redis)
2. **Implement linking mechanism** in iOS app and backend
3. **Add helper functions** for user and plan lookup
4. **Test user identification flow**

### Phase 3: Webhook Handlers
1. **Implement JWT verification** with Apple's public keys
2. **Add subscription creation/renewal handler**
3. **Add cancellation and expiration handlers**
4. **Add refund and billing error handlers**
5. **Test each notification type**

### Phase 4: Production Readiness
1. **Implement certificate chain validation**
2. **Add idempotency checking**
3. **Add comprehensive logging and monitoring**
4. **Set up Apple Store Connect webhook URLs**
5. **Test with Apple's sandbox environment**

---

## Best Practices

### 1. Error Handling
- **Always return HTTP 200** for successfully processed webhooks, even if business logic fails
- **Return HTTP 4xx/5xx** only for malformed requests or system errors
- **Log all webhook events** for debugging and analytics

### 2. Monitoring
```go
// Add metrics for webhook processing
func (s *service) ProcessAppStoreWebhook(ctx context.Context, signedPayload string) error {
	start := time.Now()
	defer func() {
		metrics.RecordWebhookProcessingTime("appstore", time.Since(start))
	}()

	// ... implementation
}
```

### 3. Database Transactions
```go
// Use transactions for critical operations
func (s *service) HandleAppStoreSubscriptionCreated(ctx context.Context, notification AppStoreNotification) error {
	return s.repo.WithTransaction(ctx, func(ctx context.Context) error {
		// Create subscription
		// Update user access
		// Log payment
		return nil
	})
}
```

### 4. Graceful Degradation
- **Handle unknown notification types** gracefully
- **Continue processing** even if optional operations fail
- **Implement circuit breakers** for external dependencies

---

## Troubleshooting

### Common Issues

1. **JWT Verification Fails**
   - Check Apple's public key rotation
   - Verify certificate chain validation
   - Ensure correct signing algorithm (ES256)

2. **User Not Found for Transaction**
   - Verify user-transaction linking implementation
   - Check transaction ID format and storage
   - Implement fallback user identification

3. **Duplicate Webhook Processing**
   - Implement idempotency using `notificationUUID`
   - Use database constraints on provider transaction IDs
   - Add webhook processing status tracking

4. **Subscription Status Conflicts**
   - Handle race conditions between webhooks
   - Use optimistic locking for subscription updates
   - Implement webhook ordering based on timestamps

### Debugging Tools

```go
// Add debug logging for webhook payloads
func (s *service) ProcessAppStoreWebhook(ctx context.Context, signedPayload string) error {
	if os.Getenv("DEBUG_WEBHOOKS") == "true" {
		log.Printf("Raw Apple webhook payload: %s", signedPayload)
	}
	// ... implementation
}
```

---

## Migration from Apple Pay to App Store

If you're migrating from the existing `PaymentProviderApplePay` to `PaymentProviderAppStore`:

1. **Update existing subscriptions**:
```sql
db.subscriptions.updateMany(
  { "provider": "applepay" },
  { $set: { "provider": "appstore" } }
)
```

2. **Update existing payments**:
```sql
db.payments.updateMany(
  { "provider": "applepay" },
  { $set: { "provider": "appstore" } }
)
```

3. **Maintain backward compatibility** during transition period
4. **Update client applications** to use new provider constant

This documentation provides a complete guide for implementing App Store subscription management that follows your existing billing system architecture and patterns.
```
```

