package google

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
)

type Service interface {
	// Core Auth
	Register(googleUserDetails GoogleUserDetails, onboarding *model.Onboarding, photo *multipart.FileHeader, photoURL string, referralCode string) (*token.Token, error)
	Login(googleUserDetails GoogleUserDetails) (*model.User, *token.Token, error)

	// Utility
	GetUserInfoFromGoogle(token string) (GoogleUserDetails, error)
	RegisterInformation() *GoogleRegisterInformation

	LegacyRegister(googleUserDetails GoogleUserDetails, onboarding *model.Onboarding, referralCode string) (*token.Token, error)
}

// GoogleRegisterInformation
type GoogleRegisterInformation struct {
	Access     string
	Onboarding *model.Onboarding
	FCMToken   string `json:"fcm"`
}

// GoogleUserDetails
type GoogleUserDetails struct {
	ID             string
	Email          string
	Verified_Email bool
	Name           string
	Given_Name     string
	Family_Name    string
	Picture        string
	Locale         string
	RegisterSource string
}

type service struct {
	Config                    *oauth2.Config
	OauthStateGoogle          string
	UserService               user.Service
	BillingService            billing.Service
	S3Service                 s3.Service
	GoogleRegisterInformation GoogleRegisterInformation
}

func New(userService user.Service, billingService billing.Service, s3Service s3.Service) Service {
	return &service{
		Config: &oauth2.Config{
			ClientID:     os.Getenv("GOOGLE_CLIENT_ID"),
			ClientSecret: os.Getenv("GOOGLE_CLIENT_SECRET"),
			RedirectURL:  os.Getenv("GOOGLE_REDIRECT_URL"),
			Scopes:       []string{"https://www.googleapis.com/auth/userinfo.email", "https://www.googleapis.com/auth/userinfo.profile"},
			Endpoint:     google.Endpoint,
		},
		OauthStateGoogle: "",
		UserService:      userService,
		BillingService:   billingService,
		S3Service:        s3Service,
	}
}

// Core Auth
// Register
func (s *service) Register(googleUserDetails GoogleUserDetails, onboarding *model.Onboarding, photo *multipart.FileHeader, photoURL string, referralCode string) (*token.Token, error) {
	user := &model.User{
		Name:     googleUserDetails.Given_Name,
		LastName: googleUserDetails.Family_Name,
		Email:    googleUserDetails.Email,
		Password: s.randomOAuthStateString(10) + "@" + s.randomOAuthStateString(10),
		//PhotoURL:   googleUserDetails.Picture, // Default to Google profile picture
		Onboarding:     onboarding,
		RegisterSource: googleUserDetails.RegisterSource,
	}

	// If a custom photo is provided, handle photo upload
	if photoURL != "" {
		user.PhotoURL = photoURL
	} else if photo != nil {
		uploadedPhotoURL, err := s.S3Service.UploadFile(context.Background(), photo, os.Getenv("AWS_S3_USER_PHOTOS_FOLDER"))
		if err != nil {
			return nil, err
		}
		user.PhotoURL = uploadedPhotoURL
	}

	if err := user.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.UserService.Create(context.TODO(), user, referralCode); err != nil {
		return nil, err
	}

	createdUser, err := s.UserService.FindByEmail(context.TODO(), user.Email)
	if err != nil {
		return nil, err
	}

	// Link any existing subscriptions to the newly created user
	userObjectID, err := primitive.ObjectIDFromHex(createdUser.ID)
	if err == nil {
		// Don't fail registration if subscription linking fails, just log it
		if err := s.BillingService.LinkExistingSubscriptionsToUser(context.TODO(), userObjectID, createdUser.Email); err != nil {
			// Log error but continue with registration
			// TODO: Consider using structured logging here
		}
	}

	token, err := token.Create(createdUser)
	if err != nil {
		return nil, err
	}

	return token, nil
}

func (s *service) Login(googleUserDetails GoogleUserDetails) (*model.User, *token.Token, error) {
	user, err := s.UserService.FindByEmail(context.TODO(), googleUserDetails.Email)
	if err != nil {
		return nil, nil, err
	}

	// Log the user in.
	err = user.PrepareLogin()
	if err != nil {
		return nil, nil, err
	}

	token, err := token.Create(user)
	if err != nil {
		return nil, nil, err
	}

	return user, token, nil
}

// Utility
// GetUserInfoFromGoogle
func (s *service) GetUserInfoFromGoogle(token string) (GoogleUserDetails, error) {
	var googleUserDetails GoogleUserDetails

	googleUserDetailsRequest, _ := http.NewRequest("GET", "https://www.googleapis.com/oauth2/v2/userinfo?access_token="+url.QueryEscape(token), nil)
	googleUserDetailsResponse, googleUserDetailsResponseError := http.DefaultClient.Do(googleUserDetailsRequest)

	if googleUserDetailsResponseError != nil {
		return GoogleUserDetails{}, errors.NewInternalError(errors.Service, "failed to fetch google user details", errors.KeyGoogleErrorFetchFailed, googleUserDetailsResponseError)
	}

	// If you want show user information
	// response, _ := ioutil.ReadAll(googleUserDetailsResponse.Body)
	// log.Println((string(response)))

	decoder := json.NewDecoder(googleUserDetailsResponse.Body)
	decoderErr := decoder.Decode(&googleUserDetails)
	defer googleUserDetailsResponse.Body.Close()

	if decoderErr != nil {
		return GoogleUserDetails{}, errors.NewInternalError(errors.Service, "failed to decode google user details", errors.KeyGoogleErrorDecodeFailed, decoderErr)
	}

	return googleUserDetails, nil
}

// RegisterInformation will return the google register information.
func (s *service) RegisterInformation() *GoogleRegisterInformation {
	return &s.GoogleRegisterInformation
}

// Helper
// randomOAuthStateString will return a random string to increase security.
func (s *service) randomOAuthStateString(n int) string {
	data := make([]byte, n)
	if _, err := io.ReadFull(rand.Reader, data); err != nil {
		return ""
	}

	return base64.StdEncoding.EncodeToString(data)
}

// LegacyRegister
func (s *service) LegacyRegister(googleUserDetails GoogleUserDetails, onboarding *model.Onboarding, referralCode string) (*token.Token, error) {
	user := &model.User{
		Name:       googleUserDetails.Name,
		Email:      googleUserDetails.Email,
		Password:   s.randomOAuthStateString(10) + "@" + s.randomOAuthStateString(10),
		PhotoURL:   googleUserDetails.Picture,
		Onboarding: onboarding,
	}

	if err := user.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.UserService.Create(context.TODO(), user, referralCode); err != nil {
		return nil, err
	}

	createdUser, err := s.UserService.FindByEmail(context.TODO(), user.Email)
	if err != nil {
		return nil, err
	}

	// Link any existing subscriptions to the newly created user
	userObjectID, err := primitive.ObjectIDFromHex(createdUser.ID)
	if err == nil {
		// Don't fail registration if subscription linking fails, just log it
		if err := s.BillingService.LinkExistingSubscriptionsToUser(context.TODO(), userObjectID, createdUser.Email); err != nil {
			// Log error but continue with registration
			// TODO: Consider using structured logging here
		}
	}

	token, err := token.Create(createdUser)
	if err != nil {
		return nil, err
	}

	return token, nil
}
