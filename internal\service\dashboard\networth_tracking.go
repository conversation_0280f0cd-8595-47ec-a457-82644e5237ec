package dashboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NetworthComponent represents the different components of networth
type NetworthComponent string

const (
	NetworthComponentStrategicFund NetworthComponent = "strategic_fund"
	NetworthComponentInvestments   NetworthComponent = "investments"
	NetworthComponentAssets        NetworthComponent = "assets"
)

// createNetworthTransaction creates a hidden transaction for networth tracking
func (s *service) createNetworthTransaction(ctx context.Context, userID string, component NetworthComponent, value monetary.Amount, date time.Time) error {
	// Get the user's financial record
	record, err := s.FinancialSheetService.FindByUser(ctx, userID)
	if err != nil {
		return err
	}

	// Create hidden transaction based on component type
	var categoryIdentifier financialsheet.CategoryIdentifier
	switch component {
	case NetworthComponentStrategicFund:
		categoryIdentifier = financialsheet.CategoryIdentifierNetworthStrategicFund
	case NetworthComponentInvestments:
		categoryIdentifier = financialsheet.CategoryIdentifierNetworthInvestments
	case NetworthComponentAssets:
		categoryIdentifier = financialsheet.CategoryIdentifierNetworthAssets
	default:
		return errors.New(errors.Service, "invalid networth component", errors.Validation, nil)
	}

	// Create the hidden transaction
	transaction := &financialsheet.Transaction{
		ObjectID:      primitive.NewObjectID(),
		MoneySource:   financialsheet.MoneySourceNetworthTracking,
		PaymentMethod: financialsheet.PaymentMethodNetworthTracking,
		Icon:          financialsheet.CategoryIconUndefined, // Use undefined icon for hidden transactions
		Category:      categoryIdentifier,
		Value:         value,
		Date:          date,
		Type:          financialsheet.CategoryTypeNetworthTracking,
		Hidden:        true, // Mark as hidden
	}

	// Create the transaction using the financial sheet service
	_, err = s.FinancialSheetService.CreateTransaction(ctx, record, transaction, false, time.UTC)
	return err
}

// Helper function to get the first day of the month for consistent dating
func getMonthStart(date time.Time) time.Time {
	return time.Date(date.Year(), date.Month(), 1, 0, 0, 0, 0, time.UTC)
}

// calculateNetworthHistory calculates networth history from hidden transactions
func (s *service) calculateNetworthHistory(ctx context.Context, userID string, months int) ([]*dashboard.NetWorthSnapshot, error) {
	// Get all hidden networth tracking transactions
	transactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeNetworthTracking, 0, 0, false)
	if err != nil {
		return nil, err
	}

	// Group transactions by month and component
	monthlyData := make(map[string]map[NetworthComponent]monetary.Amount)

	for _, transaction := range transactions {
		// Skip non-hidden transactions (safety check)
		if !transaction.Hidden {
			continue
		}

		// Get month key (YYYY-MM format)
		monthKey := transaction.Date.Format("2006-01")

		// Initialize month data if not exists
		if monthlyData[monthKey] == nil {
			monthlyData[monthKey] = make(map[NetworthComponent]monetary.Amount)
		}

		// Determine component type from category
		var component NetworthComponent
		switch transaction.Category {
		case financialsheet.CategoryIdentifierNetworthStrategicFund:
			component = NetworthComponentStrategicFund
		case financialsheet.CategoryIdentifierNetworthInvestments:
			component = NetworthComponentInvestments
		case financialsheet.CategoryIdentifierNetworthAssets:
			component = NetworthComponentAssets
		default:
			continue // Skip unknown categories
		}

		// Store the latest value for this component in this month
		// TODO later remove the duplicate hidden transaction from each month.
		// log.Println("transaction.Value", transaction.Value)
		// log.Println("transaction.Category", transaction.Category)
		monthlyData[monthKey][component] = transaction.Value
	}

	// Convert to sorted snapshots
	var snapshots []*dashboard.NetWorthSnapshot

	// Get current month as starting point
	now := time.Now()
	currentMonth := getMonthStart(now)

	// Generate snapshots for the requested number of months (going backwards)
	for i := 0; i < months; i++ {
		targetMonth := currentMonth.AddDate(0, -i, 0)
		monthKey := targetMonth.Format("2006-01")

		// Get values for this month, defaulting to 0 if no data
		monthData := monthlyData[monthKey]
		strategicFund := monthData[NetworthComponentStrategicFund]
		investments := monthData[NetworthComponentInvestments]
		assets := monthData[NetworthComponentAssets]

		snapshot := &dashboard.NetWorthSnapshot{
			UserID:             userID,
			Date:               targetMonth,
			StrategicFundValue: strategicFund,
			InvestmentsValue:   investments,
			AssetsValue:        assets,
			TotalValue:         strategicFund + investments + assets,
		}

		snapshots = append(snapshots, snapshot)
	}

	// Reverse the slice to have oldest first
	for i, j := 0, len(snapshots)-1; i < j; i, j = i+1, j-1 {
		snapshots[i], snapshots[j] = snapshots[j], snapshots[i]
	}

	return snapshots, nil
}
