package user

type Employee struct {
	Name         string `json:"name"`
	Email        string `json:"email"`
	Sequence     int    `json:"sequence"`
	DCoins       int    `json:"dcoins"`
	Achievements int    `json:"achievements"`
}

type JourneyProgression struct {
	Name       string  `json:"name"`
	Percentage float64 `json:"percentage"`
}

type DreamsProgression struct {
	Registered int     `json:"registered"`
	Completed  int     `json:"completed"`
	Progress   float64 `json:"progress"`
}

type FinancialProfileDistribution struct {
	Undefined    float64 `json:"undefined"`
	Overindebted float64 `json:"overindebted"`
	Indebted     float64 `json:"indebted"`
	Balanced     float64 `json:"balanced"`
	Investor     float64 `json:"investor"`
}

type AgeRanges struct {
	LessThan12 int `json:"lessThan12"`
	From12To17 int `json:"from12To17"`
	From18To29 int `json:"from18To29"`
	From30To59 int `json:"from30To59"`
	Greater60  int `json:"greater60"`
}

type FinancialSituations struct {
	CantPayBills        float64 `json:"cantPayBills"`
	InDebtButManaging   float64 `json:"inDebtButManaging"`
	NoDebtButCantSave   float64 `json:"noDebtButCantSave"`
	SavesMonthly        float64 `json:"savesMonthly"`
	InvestsAndWantsMore float64 `json:"investsAndWantsMore"`
}

// FinancialProfileDatapoint represents the data for a single financial profile within a given month.
type FinancialProfileDatapoint struct {
	FinancialProfile string  `json:"financialProfile"`
	Count            int     `json:"count"`
	Percentage       float64 `json:"percentage"`
}

// MonthlyFinancialProfileEvolution holds the aggregated data for a single month.
type MonthlyFinancialProfileEvolution struct {
	Month      string                       `json:"month"` // Format "YYYY-MM"
	Datapoints []*FinancialProfileDatapoint `json:"datapoints"`
}

type PersonalInterests struct {
	Fashion          int `json:"fashion"`
	Family           int `json:"family"`
	Pets             int `json:"pets"`
	Travel           int `json:"travel"`
	Nature           int `json:"nature"`
	Music            int `json:"music"`
	SelfCare         int `json:"selfCare"`
	Books            int `json:"books"`
	Shopping         int `json:"shopping"`
	Movies           int `json:"movies"`
	Games            int `json:"games"`
	Fitness          int `json:"fitness"`
	Technology       int `json:"technology"`
	Languages        int `json:"languages"`
	Astrology        int `json:"astrology"`
	Food             int `json:"food"`
	Work             int `json:"work"`
	Culture          int `json:"culture"`
	Sports           int `json:"sports"`
	SpaceExploration int `json:"spaceExploration"`
	Crypto           int `json:"crypto"`
	Health           int `json:"health"`
}

type FinancialGoals struct {
	DebtControl       int `json:"debtControl"`
	MoneySavings      int `json:"moneySavings"`
	StrategicFund     int `json:"strategicFund"`
	FutureInvestments int `json:"futureInvestments"`
	FinancialLiteracy int `json:"financialLiteracy"`
}
