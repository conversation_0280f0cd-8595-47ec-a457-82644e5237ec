package gamification

import "github.com/dsoplabs/dinbora-backend/internal/model/progression"

func (s *service) hasCompletedChallengePhase(summary *progression.ProgressSummary, phaseID string) bool {
	if summary == nil || phaseID == "" {
		return false
	}

	for _, trail := range summary.Trails {
		if hasCompletedChallengePhaseInTrail(trail, phaseID) {
			return true
		}
	}
	return false
}

func hasCompletedChallengePhaseInTrail(trail *progression.TrailSummary, phaseID string) bool {
	if trail == nil {
		return false
	}

	return hasCompletedChallengePhaseInProgress(trail.ChallengeProgress, phaseID)
}

func hasCompletedChallengePhaseInProgress(challenge *progression.ChallengeProgress, phaseID string) bool {
	if challenge == nil {
		return false
	}
	return hasCompletedPhase(challenge.Phases, phaseID)
}

func hasCompletedPhase(phases map[string]*progression.PhaseProgress, phaseID string) bool {
	for id, phase := range phases {
		if id == phaseID && phase != nil && phase.Completed {
			return true
		}
	}
	return false
}
