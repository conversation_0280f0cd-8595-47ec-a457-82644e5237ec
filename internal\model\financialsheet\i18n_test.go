package financialsheet

import (
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/stretchr/testify/assert"
)

// TestModelErrorsHaveTranslationKeys tests that all model layer errors use translation keys
func TestModelErrorsHaveTranslationKeys(t *testing.T) {
	tests := []struct {
		name           string
		setupFunc      func() error
		expectedKey    string
		expectedKind   errors.Kind
	}{
		{
			name: "MoneySource - invalid value",
			setupFunc: func() error {
				ms := MoneySource(255) // Invalid value
				return ms.Validate()
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidMoneySource,
			expectedKind: errors.Validation,
		},
		{
			name: "PaymentMethod - invalid value",
			setupFunc: func() error {
				pm := PaymentMethod(255) // Invalid value
				return pm.Validate()
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidPaymentMethod,
			expectedKind: errors.Validation,
		},
		{
			name: "CategoryIdentifier - invalid value",
			setupFunc: func() error {
				ci := CategoryIdentifier("invalid-identifier")
				return ci.Validate()
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidCategoryIdentifier,
			expectedKind: errors.Validation,
		},
		{
			name: "CategoryType - invalid value",
			setupFunc: func() error {
				ct := CategoryType("invalid-type")
				return ct.Validate()
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidCategoryType,
			expectedKind: errors.Validation,
		},
		{
			name: "CategoryBackground - invalid value",
			setupFunc: func() error {
				cb := CategoryBackground("invalid-background")
				return cb.Validate()
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidCategoryBackground,
			expectedKind: errors.Validation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Act
			err := tt.setupFunc()

			// Assert
			assert.Error(t, err)
			
			domainErr, ok := err.(*errors.DomainError)
			assert.True(t, ok, "Error should be a DomainError")
			assert.Equal(t, tt.expectedKind, domainErr.Kind())
			assert.Equal(t, tt.expectedKey, domainErr.TranslationKey())
		})
	}
}

// TestModelTranslationKeysExist verifies that all model translation keys are properly defined
func TestModelTranslationKeysExist(t *testing.T) {
	translationKeys := []string{
		errors.KeyFinancialSheetErrorInvalidMoneySource,
		errors.KeyFinancialSheetErrorInvalidPaymentMethod,
		errors.KeyFinancialSheetErrorInvalidCategoryIdentifier,
		errors.KeyFinancialSheetErrorInvalidCategoryType,
		errors.KeyFinancialSheetErrorInvalidCategoryBackground,
	}

	// Verify that all keys are non-empty strings and follow the expected pattern
	for _, key := range translationKeys {
		assert.NotEmpty(t, key, "Translation key should not be empty")
		assert.Contains(t, key, "financialsheet.error.", "Translation key should contain 'financialsheet.error.'")
	}
}

// TestModelTranslationKeyFormat verifies that translation keys follow the correct format
func TestModelTranslationKeyFormat(t *testing.T) {
	tests := []struct {
		name string
		key  string
	}{
		{"Invalid Money Source", errors.KeyFinancialSheetErrorInvalidMoneySource},
		{"Invalid Payment Method", errors.KeyFinancialSheetErrorInvalidPaymentMethod},
		{"Invalid Category Identifier", errors.KeyFinancialSheetErrorInvalidCategoryIdentifier},
		{"Invalid Category Type", errors.KeyFinancialSheetErrorInvalidCategoryType},
		{"Invalid Category Background", errors.KeyFinancialSheetErrorInvalidCategoryBackground},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Verify the key follows the expected pattern: financialsheet.error.specificError
			assert.Regexp(t, `^financialsheet\.error\.[a-zA-Z][a-zA-Z0-9]*$`, tt.key, 
				"Translation key should follow pattern: financialsheet.error.specificError")
		})
	}
}

// TestModelExpectedTranslationKeyValues verifies that translation keys have the expected values
func TestModelExpectedTranslationKeyValues(t *testing.T) {
	expectedValues := map[string]string{
		errors.KeyFinancialSheetErrorInvalidMoneySource:        "financialsheet.error.invalidMoneySource",
		errors.KeyFinancialSheetErrorInvalidPaymentMethod:      "financialsheet.error.invalidPaymentMethod",
		errors.KeyFinancialSheetErrorInvalidCategoryIdentifier: "financialsheet.error.invalidCategoryIdentifier",
		errors.KeyFinancialSheetErrorInvalidCategoryType:       "financialsheet.error.invalidCategoryType",
		errors.KeyFinancialSheetErrorInvalidCategoryBackground: "financialsheet.error.invalidCategoryBackground",
	}

	for constant, expectedValue := range expectedValues {
		assert.Equal(t, expectedValue, constant, "Translation key constant should have the expected value")
	}
}

// TestModelValidationBehavior tests that validation works correctly for valid values
func TestModelValidationBehavior(t *testing.T) {
	tests := []struct {
		name      string
		setupFunc func() error
	}{
		{
			name: "MoneySource - valid value",
			setupFunc: func() error {
				ms := MoneySourceOpt1
				return ms.Validate()
			},
		},
		{
			name: "PaymentMethod - valid value",
			setupFunc: func() error {
				pm := PaymentMethodOpt1
				return pm.Validate()
			},
		},
		{
			name: "CategoryIdentifier - valid value",
			setupFunc: func() error {
				ci := CategoryIdentifierCompensation
				return ci.Validate()
			},
		},
		{
			name: "CategoryType - valid value",
			setupFunc: func() error {
				ct := CategoryTypeIncome
				return ct.Validate()
			},
		},
		{
			name: "CategoryBackground - valid value",
			setupFunc: func() error {
				cb := CategoryBackgroundIncome
				return cb.Validate()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Act
			err := tt.setupFunc()

			// Assert
			assert.NoError(t, err, "Valid values should not return errors")
		})
	}
}

// TestModelJSONUnmarshalErrors tests that JSON unmarshaling errors use translation keys
func TestModelJSONUnmarshalErrors(t *testing.T) {
	tests := []struct {
		name           string
		setupFunc      func() error
		expectedKey    string
		expectedKind   errors.Kind
	}{
		{
			name: "CategoryIdentifier - JSON unmarshal invalid value",
			setupFunc: func() error {
				var ci CategoryIdentifier
				return ci.UnmarshalJSON([]byte(`"invalid-identifier"`))
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidCategoryIdentifier,
			expectedKind: errors.Validation,
		},
		{
			name: "CategoryType - JSON unmarshal invalid value",
			setupFunc: func() error {
				var ct CategoryType
				return ct.UnmarshalJSON([]byte(`"invalid-type"`))
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidCategoryType,
			expectedKind: errors.Validation,
		},
		{
			name: "CategoryBackground - JSON unmarshal invalid value",
			setupFunc: func() error {
				var cb CategoryBackground
				return cb.UnmarshalJSON([]byte(`"invalid-background"`))
			},
			expectedKey:  errors.KeyFinancialSheetErrorInvalidCategoryBackground,
			expectedKind: errors.Validation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Act
			err := tt.setupFunc()

			// Assert
			assert.Error(t, err)
			
			domainErr, ok := err.(*errors.DomainError)
			assert.True(t, ok, "Error should be a DomainError")
			assert.Equal(t, tt.expectedKind, domainErr.Kind())
			assert.Equal(t, tt.expectedKey, domainErr.TranslationKey())
		})
	}
}
