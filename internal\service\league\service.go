package league

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/league"
	_league "github.com/dsoplabs/dinbora-backend/internal/repository/league"
	// _user "github.com/dsoplabs/dinbora-backend/internal/service/user" // Removed UserService dependency
)

type Service interface {
	// CRUD
	CreateLeague(ctx context.Context, ownerUserID string, ownerUserName string, ownerPhotoURL string, leagueName string, startDate time.Time, endDate time.Time, userTimezone *time.Location) (*league.League, error)
	FindLeague(ctx context.Context, leagueID string, userID string) (*league.League, error) // Now requires userID for access control
	FindAllLeagues(ctx context.Context, userID string) ([]*league.League, error)            // Get all leagues a user is part of
	PatchLeague(ctx context.Context, leagueID string, userID string, name *string) (*league.League, error)
	DeleteLeague(ctx context.Context, leagueID string, userID string) error // Ensure only owner can delete

	// Invitation
	InviteDetails(ctx context.Context, code string) (*league.League, error) // Get league with invite code
	JoinLeague(ctx context.Context, userID string, userName string, photoURL string, inviteCode string) (*league.League, error)
	LeaveLeague(ctx context.Context, leagueID string, userID string) error
	// RemoveMemberFromLeague(ctx context.Context, leagueID string, ownerUserID string, memberUserID string) error // Admin/owner action

	// Gameplay
	RecordTransactionForAllUserLeagues(ctx context.Context, userID string, transactionDate time.Time) error
	StartNewSeasonInLeague(ctx context.Context, leagueID string, userID string, startDate time.Time, endDate time.Time) (*league.League, error) // Owner action
	// GetLeagueSeasonHistory(ctx context.Context, leagueID string) ([]league.Season, error)
	FindLeagueRanking(ctx context.Context, leagueID string, userID string) (*league.LeagueRanking, error)

	// Cards
	FindLeagueCard(ctx context.Context, leagueID string, userID string, userTimezone *time.Location) (*league.LeagueCard, error)
	FindAllLeaguesCards(ctx context.Context, userID string, userTimezone *time.Location) ([]*league.LeagueCard, error)
}

type service struct {
	Repository _league.Repository
	// UserService _user.Service // Removed UserService dependency
}

func New(repository _league.Repository) Service {
	return &service{
		Repository: repository,
		// UserService: userService, // Removed UserService dependency
	}
}
