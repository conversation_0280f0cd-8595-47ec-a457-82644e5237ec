package progression

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/repository"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	// NEW API - Summary / Events
	events    *mongo.Collection
	summaries *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		events:    db.Collection(repository.PROGRESSIONS_EVENTS),
		summaries: db.Collection(repository.PROGRESSIONS_SUMMARIES),
	}

	indexes := []mongo.IndexModel{
		{
			Keys: bson.D{{Key: "userId", Value: 1}, {Key: "timestamp", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "userId", Value: 1}, {Key: "trailId", Value: 1}, {Key: "timestamp", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "userId", Value: 1}, {Key: "itemId", Value: 1}, {Key: "itemType", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "userId", Value: 1}, {Key: "action", Value: 1}, {Key: "timestamp", Value: 1}},
		},
	}

	_, err := repo.events.Indexes().CreateMany(context.Background(), indexes)

	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on progressions.events field")
		if err := db.Client().Disconnect(context.Background()); err != nil {
			log.Println("warning: failed to disconnect from MongoDB client")
		}
	}

	summaryIndexes := []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "userId", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("userId"),
		},
	}

	_, err = repo.summaries.Indexes().CreateMany(context.Background(), summaryIndexes)

	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on progressions.summary field")
		if err := db.Client().Disconnect(context.Background()); err != nil {
			log.Println("warning: failed to disconnect from MongoDB client")
		}
	}

	return repo
}

// region new event api
func (m mongoDB) CreateEvent(ctx context.Context, event *progression.ProgressEvent) error {
	event.SetDefaults()

	if !event.IsValid() {
		return errors.NewValidationError(errors.Repository, "Event is not valid", errors.KeyProgressionErrorInvalidEvent, nil)
	}

	_, err := m.events.InsertOne(ctx, event)
	return err
}

func (m mongoDB) CreateEventsBatch(ctx context.Context, events []*progression.ProgressEvent) error {
	if len(events) == 0 {
		return nil
	}

	docs := make([]interface{}, len(events))
	for i, event := range events {
		event.SetDefaults()
		if !event.IsValid() {
			return errors.NewValidationError(errors.Repository, "Event is not valid", errors.KeyProgressionErrorInvalidEvent, nil)
		}
		docs[i] = event
	}

	_, err := m.events.InsertMany(ctx, docs)
	return err
}

func (m mongoDB) GetUserEvents(ctx context.Context, userID string, limit int) ([]*progression.ProgressEvent, error) {
	filter := bson.M{"userId": userID}
	opts := options.Find().SetSort(bson.D{{Key: "timestamp", Value: 1}})

	if limit > 0 {
		opts.SetLimit(int64(limit))
	}

	cursor, err := m.events.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var events []*progression.ProgressEvent
	err = cursor.All(ctx, &events)
	return events, err
}

func (m mongoDB) GetTrailEvents(ctx context.Context, userID string, trailID string) ([]*progression.ProgressEvent, error) {
	filter := bson.M{"userId": userID, "trailId": trailID}
	opts := options.Find().SetSort(bson.D{{Key: "timestamp", Value: 1}})

	cursor, err := m.events.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var events []*progression.ProgressEvent
	if err = cursor.All(ctx, &events); err != nil {
		return nil, err
	}

	return events, nil
}

func (m mongoDB) GetEventsByTimeRange(ctx context.Context, userID string, start, end time.Time) ([]*progression.ProgressEvent, error) {
	filter := bson.M{
		"userId": userID,
		"timestamp": bson.M{
			"$gte": start,
			"$lte": end,
		},
	}
	opts := options.Find().SetSort(bson.D{{Key: "timestamp", Value: 1}})

	cursor, err := m.events.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var events []*progression.ProgressEvent
	err = cursor.All(ctx, &events)
	return events, err
}

func (m mongoDB) GetProgressSummary(ctx context.Context, userID string) (*progression.ProgressSummary, error) {
	var progressSummary progression.ProgressSummary
	if err := m.summaries.FindOne(ctx,
		bson.D{primitive.E{Key: "userId", Value: userID}}).Decode(&progressSummary); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.NewNotFoundError(errors.Repository, "summary not found for user", errors.KeyProgressionErrorNotFound, err)
		}
		return nil, errors.NewNotFoundError(errors.Repository, "failed to find summary by user", errors.KeyProgressionErrorNotFoundForUser, err)
	}

	return &progressSummary, nil
}

func (m mongoDB) GetProgressSummaryBatch(ctx context.Context, userIds []string) ([]*progression.ProgressSummary, error) {
	filter := bson.M{"userId": bson.M{"$in": userIds}}
	opts := options.Find().SetSort(bson.D{{Key: "userId", Value: 1}})

	cursor, err := m.summaries.Find(ctx, filter, opts)
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "failed to find summary by users", errors.KeyProgressionErrorNotFoundForUser, err) // TODO - Fix error key
	}
	defer cursor.Close(ctx)

	var summaries []*progression.ProgressSummary
	err = cursor.All(ctx, &summaries)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.NewNotFoundError(errors.Repository, "summary not found for users", errors.KeyProgressionErrorNotFound, err) // TODO - Fix error key
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to decode summary", errors.KeyProgressionErrorNotFoundForUser, err) // TODO - Fix error key
	}
	return summaries, nil
}

func (m mongoDB) SaveProgressSummary(ctx context.Context, summary *progression.ProgressSummary) error {
	summary.UpdatedAt = time.Now()
	summary.Revision++

	filter := bson.M{"userId": summary.UserID}
	update := bson.M{"$set": summary}
	opts := options.Update().SetUpsert(true)

	_, err := m.summaries.UpdateOne(ctx, filter, update, opts)
	return err
}

func (m mongoDB) InvalidateProgressSummary(ctx context.Context, userID string) error {
	_, err := m.summaries.DeleteOne(ctx, bson.M{"userId": userID})
	return err
}
