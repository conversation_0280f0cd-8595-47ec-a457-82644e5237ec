package migrations

// This will update all users financial profile based on the points in the financial profile challenge phase

import (
	"context"
	"fmt"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type UpdateUserFinancialProfile struct {
	db          *mongo.Database
	userService user.Service
}

func NewUpdateUserFinancialProfile(db *mongo.Database, userService user.Service) *UpdateUserFinancialProfile {
	return &UpdateUserFinancialProfile{
		db:          db,
		userService: userService,
	}
}

func (m *UpdateUserFinancialProfile) Name() string {
	return "update_user_profile_20250828_fix000"
}

func (m *UpdateUserFinancialProfile) Up(ctx context.Context) error {
	// Clean the old financial profile data because was an integer and now is model.FiancialProfile struct
	m.CleanOldFinancialProfileData(ctx)

	// Get all users
	userCollection := m.db.Collection(repository.USERS_COLLECTION)
	cursor, err := userCollection.Find(ctx, bson.D{})
	if err != nil {
		return fmt.Errorf("failed to find users: %w", err)
	}
	defer cursor.Close(ctx)

	successCount := 0
	errorCount := 0

	for cursor.Next(ctx) {
		var user model.User
		if err := cursor.Decode(&user); err != nil {
			log.Printf("Error decoding user: %v", err)
			errorCount++
			continue
		}

		user.ID = user.ObjectID.Hex()

		// Set the user financial profile based on the points in the financial profile challenge phase
		err = m.userService.UpdateFinancialProfile(ctx, user.ID)
		if err != nil {
			log.Printf("Error setting user financial profile for user %s (%s): %v",
				user.Name, user.ID, err)
			errorCount++
			continue
		}

		log.Printf("Updated financial profile for user: %s (%s)",
			user.Name, user.ID)
		successCount++
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor iteration error: %w", err)
	}

	log.Printf("User financial profile migration completed - Updated: %d, Errors: %d",
		successCount, errorCount)

	return nil
}

func (m *UpdateUserFinancialProfile) CleanOldFinancialProfileData(ctx context.Context) error {
	// Get all users
	userCollection := m.db.Collection(repository.USERS_COLLECTION)
	_, err := userCollection.UpdateMany(ctx, bson.D{}, bson.D{{Key: "$unset", Value: bson.D{{Key: "financialProfile", Value: ""}}}})
	if err != nil {
		return fmt.Errorf("failed to clean old financial profile data: %w", err)
	}

	return nil
}

func (m *UpdateUserFinancialProfile) Down(ctx context.Context) error {
	return nil
}
