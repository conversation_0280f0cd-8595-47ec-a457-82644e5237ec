package progression

import (
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ProgressEventTestSuite struct {
	suite.Suite
}

func (s *ProgressEventTestSuite) TestIsValid_ValidEvent() {
	validID := primitive.NewObjectID().Hex()
	event := &ProgressEvent{
		UserID:    validID,
		TrailID:   validID,
		ItemID:    validID,
		ContentID: validID,
		ItemType:  LessonProgressType,
		Action:    StartedActionType,
		Timestamp: time.Now(),
	}
	s.True(event.IsValid(), "Expected valid event to be valid")
}

func (s *ProgressEventTestSuite) TestIsValid_InvalidItemType() {
	validID := primitive.NewObjectID().Hex()
	event := &ProgressEvent{
		UserID:    validID,
		TrailID:   validID,
		ItemID:    validID,
		ContentID: validID,
		ItemType:  ProgressType("INVALID"),
		Action:    StartedActionType,
		Timestamp: time.Now(),
	}
	s.False(event.IsValid(), "Expected event with invalid ItemType to be invalid")
}

func (s *ProgressEventTestSuite) TestIsValid_InvalidActionType() {
	validID := primitive.NewObjectID().Hex()
	event := &ProgressEvent{
		UserID:    validID,
		TrailID:   validID,
		ItemID:    validID,
		ContentID: validID,
		ItemType:  LessonProgressType,
		Action:    ActionType("INVALID"),
		Timestamp: time.Now(),
	}
	s.False(event.IsValid(), "Expected event with invalid ActionType to be invalid")
}

func (s *ProgressEventTestSuite) TestIsValid_InvalidUserID() {
	invalidID := "notanobjectid"
	validID := primitive.NewObjectID().Hex()
	event := &ProgressEvent{
		UserID:    invalidID,
		TrailID:   validID,
		ItemID:    validID,
		ContentID: validID,
		ItemType:  LessonProgressType,
		Action:    StartedActionType,
		Timestamp: time.Now(),
	}
	s.False(event.IsValid(), "Expected event with invalid UserID to be invalid")
}

func (s *ProgressEventTestSuite) TestIsValid_InvalidTrailID() {
	invalidID := "notanobjectid"
	validID := primitive.NewObjectID().Hex()
	event := &ProgressEvent{
		UserID:    validID,
		TrailID:   invalidID,
		ItemID:    validID,
		ContentID: validID,
		ItemType:  LessonProgressType,
		Action:    StartedActionType,
		Timestamp: time.Now(),
	}
	s.False(event.IsValid(), "Expected event with invalid TrailID to be invalid")
}

func TestProgressEventTestSuite(t *testing.T) {
	suite.Run(t, new(ProgressEventTestSuite))
}
