package migrations

import (
	"context"
	"fmt"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	progressionService "github.com/dsoplabs/dinbora-backend/internal/service/progression"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type MigrateProgressToEvents struct {
	db           *mongo.Database
	trailService trail.Service
}

func NewMigrateProgressToEvents(db *mongo.Database, trailService trail.Service) *MigrateProgressToEvents {
	return &MigrateProgressToEvents{
		db:           db,
		trailService: trailService,
	}
}

func (m *MigrateProgressToEvents) Name() string {
	return "migrate_progress_to_events"
}

func (m *MigrateProgressToEvents) Up(ctx context.Context) error {
	log.Printf("Starting migration: %s", m.Name())

	// Get the legacy progressions collection
	progressionsCollection := m.db.Collection(repository.PROGRESSIONS_COLLECTION)
	eventsCollection := m.db.Collection(repository.PROGRESSIONS_EVENTS)

	// Find all progressions
	cursor, err := progressionsCollection.Find(ctx, bson.D{})
	if err != nil {
		return fmt.Errorf("failed to find progressions: %w", err)
	}
	defer cursor.Close(ctx)

	var processedCount int64
	var errorCount int64
	var totalEvents int64

	// Process progressions in batches
	const batchSize = 100
	var eventsBatch []interface{}

	for cursor.Next(ctx) {
		var legacyProgression progression.Progression
		if err := cursor.Decode(&legacyProgression); err != nil {
			log.Printf("Error decoding progression document: %v", err)
			errorCount++
			continue
		}

		// Convert legacy progression to events
		events := m.convertLegacyToEvents(&legacyProgression)

		// Add events to batch
		for _, event := range events {
			eventsBatch = append(eventsBatch, event)
		}

		// Insert batch when it reaches batchSize
		if len(eventsBatch) >= batchSize {
			if err := m.insertEventsBatch(ctx, eventsCollection, eventsBatch); err != nil {
				log.Printf("Error inserting events batch: %v", err)
				errorCount++
			} else {
				totalEvents += int64(len(eventsBatch))
			}
			eventsBatch = nil // Reset batch
		}

		processedCount++
		if processedCount%100 == 0 {
			log.Printf("Processed %d progressions...", processedCount)
		}
	}

	// Insert remaining events in batch
	if len(eventsBatch) > 0 {
		if err := m.insertEventsBatch(ctx, eventsCollection, eventsBatch); err != nil {
			log.Printf("Error inserting final events batch: %v", err)
			errorCount++
		} else {
			totalEvents += int64(len(eventsBatch))
		}
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor error during migration: %w", err)
	}

	log.Printf("Migration completed: %s - Progressions processed: %d, Events created: %d, Errors: %d",
		m.Name(), processedCount, totalEvents, errorCount)

	if errorCount > 0 {
		return fmt.Errorf("migration completed with %d errors", errorCount)
	}

	return nil
}

func (m *MigrateProgressToEvents) Down(ctx context.Context) error {
	log.Printf("Starting rollback: %s", m.Name())

	eventsCollection := m.db.Collection("progression.events")

	// Remove all migrated events
	result, err := eventsCollection.DeleteMany(ctx, bson.D{
		{Key: "migrated", Value: true},
	})
	if err != nil {
		return fmt.Errorf("failed to remove migrated events: %w", err)
	}

	log.Printf("Rollback completed: %s - Removed %d events", m.Name(), result.DeletedCount)
	return nil
}

func (m *MigrateProgressToEvents) insertEventsBatch(ctx context.Context, collection *mongo.Collection, events []interface{}) error {
	if len(events) == 0 {
		return nil
	}

	_, err := collection.InsertMany(ctx, events)
	return err
}

func (m *MigrateProgressToEvents) convertLegacyToEvents(prog *progression.Progression) []*progression.ProgressEvent {
	var events []*progression.ProgressEvent
	eventCounter := 0

	for _, trail := range prog.Trails {
		trailContent, err := m.trailService.Find(context.Background(), trail.ID)
		if err != nil {
			log.Printf("Error getting trail content: %v", err)
			continue
		}

		lessonEvents := m.convertLessonEvents(prog, trail, &eventCounter)
		events = append(events, lessonEvents...)

		challengeEvents := m.convertChallengeEvents(prog, trail, trailContent.Challenge, &eventCounter)
		events = append(events, challengeEvents...)
	}

	return events
}

func (m *MigrateProgressToEvents) convertLessonEvents(userLegacyProgression *progression.Progression, legacyTrailProgression *progression.Trail, eventCounter *int) []*progression.ProgressEvent {
	var events []*progression.ProgressEvent

	for _, lesson := range legacyTrailProgression.Lessons {
		for _, content := range lesson.Path {
			eventTime := userLegacyProgression.CreatedAt.Add(time.Duration(*eventCounter) * time.Minute)

			if content.Choice.Next != "coin" {
				// Create started event for non-coin contents
				startEvent := m.createLessonEvent(userLegacyProgression, legacyTrailProgression, lesson, content, progression.StartedActionType, eventTime)
				events = append(events, startEvent)
			} else {
				// Create completion event for coin contents
				completionEvent := m.createLessonEvent(userLegacyProgression, legacyTrailProgression, lesson, content, progression.CompletedActionType, eventTime)
				completionEvent.Data["rewarded"] = lesson.Rewarded
				events = append(events, completionEvent)
			}
			*eventCounter++
		}
	}

	return events
}

func (m *MigrateProgressToEvents) convertChallengeEvents(prog *progression.Progression, trail *progression.Trail, challengeContent *content.Challenge, eventCounter *int) []*progression.ProgressEvent {
	var events []*progression.ProgressEvent

	if trail.Challenge == nil {
		return events
	}

	for _, phase := range trail.Challenge.Phases {
		for _, content := range phase.Path {
			eventTime := prog.CreatedAt.Add(time.Duration(*eventCounter) * time.Minute)

			// Create started event
			if content.Choice.Next != "coin" {
				startEvent := m.createChallengeEvent(prog, trail, phase, content, challengeContent, progression.StartedActionType, eventTime)
				events = append(events, startEvent)
			} else {
				// Create completion event if it's a coin content
				completionEvent := m.createChallengeEvent(prog, trail, phase, content, challengeContent, progression.CompletedActionType, eventTime)
				completionEvent.Data["rewarded"] = trail.Challenge.Rewarded
				events = append(events, completionEvent)
			}
			*eventCounter++
		}
	}

	return events
}

func (m *MigrateProgressToEvents) createLessonEvent(legacyProgression *progression.Progression, legacyTrailProgression *progression.Trail, legacyLessonProgression *progression.Lesson, legacyContentProgression *progression.LessonContent, action progression.ActionType, eventTime time.Time) *progression.ProgressEvent {
	return &progression.ProgressEvent{
		UserID:    legacyProgression.User,
		TrailID:   legacyTrailProgression.ID,
		ItemID:    legacyLessonProgression.Identifier,
		ItemType:  progression.LessonProgressType,
		Action:    action,
		ContentID: legacyContentProgression.Identifier,
		Choice: &progression.Choice{
			Identifier: legacyContentProgression.Choice.Identifier,
			Next:       legacyContentProgression.Choice.Next,
		},
		Data: map[string]interface{}{
			"migrated":            true,
			"originalTimestamp":   legacyContentProgression.Timestamp,
			"legacyProgressionId": legacyProgression.ObjectID.Hex(),
		},
		Timestamp: eventTime,
		Migrated:  true,
		LegacyID:  legacyProgression.ObjectID.Hex(),
	}
}

func (m *MigrateProgressToEvents) createChallengeEvent(prog *progression.Progression, trail *progression.Trail, phase *progression.ChallengePhase, content *progression.ChallengeContent, challengeContent *content.Challenge, action progression.ActionType, eventTime time.Time) *progression.ProgressEvent {
	points, err := progressionService.GetChallengePhaseChoicePoints(phase.Identifier, content.Identifier, content.Choice.Identifier, challengeContent)
	if err != nil {
		log.Printf("Error getting points for phase %s, content %s: %v", phase.Identifier, content.Identifier, err)
		points = 0
	}

	return &progression.ProgressEvent{
		UserID:    prog.User,
		TrailID:   trail.ID,
		ItemID:    phase.Identifier,
		ItemType:  progression.ChallengeProgressType,
		Action:    action,
		ContentID: content.Identifier,
		Choice: &progression.Choice{
			Identifier: content.Choice.Identifier,
			Next:       content.Choice.Next,
			Points:     points,
		},
		Data: map[string]interface{}{
			"migrated":            true,
			"phaseId":             phase.Identifier,
			"challenge":           challengeContent.Identifier,
			"originalTimestamp":   content.Timestamp,
			"legacyProgressionId": prog.ObjectID.Hex(),
		},
		Timestamp: eventTime,
		Migrated:  true,
		LegacyID:  prog.ObjectID.Hex(),
	}
}
