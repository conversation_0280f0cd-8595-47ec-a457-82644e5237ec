package dreamboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Contribution Management

// CreateContribution creates a new contribution
func (s *service) CreateContribution(ctx context.Context, contribution *dreamboard.Contribution) (*dreamboard.Contribution, error) {
	if err := contribution.Validate(); err != nil {
		return nil, err
	}

	_, err := s.Repository.CreateContribution(ctx, contribution)
	if err != nil {
		return nil, err
	}

	contribution.ID = contribution.ObjectID.Hex()
	return contribution, nil
}

// FindContributionsByDreamID retrieves all contributions for a specific dream
func (s *service) FindContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error) {
	contributions, err := s.Repository.FindContributionsByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	for _, contribution := range contributions {
		if contribution != nil && !contribution.ObjectID.IsZero() {
			contribution.ID = contribution.ObjectID.Hex()
		}
	}

	return contributions, nil
}

// FindActiveContributionsByDreamID retrieves only active contributions for a specific dream
func (s *service) FindActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error) {
	contributions, err := s.Repository.FindActiveContributionsByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	for _, contribution := range contributions {
		if contribution != nil && !contribution.ObjectID.IsZero() {
			contribution.ID = contribution.ObjectID.Hex()
		}
	}

	return contributions, nil
}

// FindContributionsByUserID retrieves all contributions for a specific user
func (s *service) FindContributionsByUserID(ctx context.Context, userID string) ([]*dreamboard.Contribution, error) {
	contributions, err := s.Repository.FindContributionsByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	for _, contribution := range contributions {
		if contribution != nil && !contribution.ObjectID.IsZero() {
			contribution.ID = contribution.ObjectID.Hex()
		}
	}

	return contributions, nil
}

// FindContributionByDreamAndUser retrieves a specific contribution by dream and user
func (s *service) FindContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*dreamboard.Contribution, error) {
	contribution, err := s.Repository.FindContributionByDreamAndUser(ctx, dreamID, userID)
	if err != nil {
		return nil, err
	}

	if contribution != nil && !contribution.ObjectID.IsZero() {
		contribution.ID = contribution.ObjectID.Hex()
	}

	return contribution, nil
}

// UpdateContributionStatus updates the status of all contributions for a dream
func (s *service) UpdateContributionStatus(ctx context.Context, dreamID string, status dreamboard.ContributionStatus) error {
	return s.Repository.UpdateContributionStatusByDreamID(ctx, dreamID, status)
}

// UpdateContribution updates a specific contribution
func (s *service) UpdateContribution(ctx context.Context, contribution *dreamboard.Contribution) error {
	if err := contribution.Validate(); err != nil {
		return err
	}

	if contribution.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(contribution.ID)
		if err != nil {
			return errors.NewValidationError(errors.Service, "invalid contribution ID", errors.KeyDreamboardErrorInvalidId, err)
		}
		contribution.ObjectID = objID
	}

	return s.Repository.UpdateContribution(ctx, contribution)
}

// DeleteContribution deletes a contribution
func (s *service) DeleteContribution(ctx context.Context, contributionID string) error {
	objID, err := primitive.ObjectIDFromHex(contributionID)
	if err != nil {
		return errors.NewWithTranslationKey(errors.Service, "invalid contribution ID", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, err)
	}

	return s.Repository.DeleteContribution(ctx, objID)
}
