package financialdna

import (
	"context"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialdna"
	_financialdna "github.com/dsoplabs/dinbora-backend/internal/service/financialdna"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	Create() echo.HandlerFunc
	Find() echo.HandlerFunc
	FindByUser() echo.HandlerFunc
	Update() echo.HandlerFunc
	Delete() echo.HandlerFunc

	// Member CRUD
	PatchMember() echo.HandlerFunc
	DeleteMember() echo.HandlerFunc

	CreateChild() echo.HandlerFunc
	DeleteChild() echo.HandlerFunc

	// Utility
	Initialize() echo.HandlerFunc
}

type controller struct {
	Service _financialdna.Service
}

func New(service _financialdna.Service) Controller {
	return &controller{
		Service: service,
	}
}

// Routes
func (c *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	financialDNAGroup := currentGroup.Group("/financialdna", middlewares.AuthGuard())

	// CRUD
	financialDNAGroup.POST("", c.Create(), middlewares.AdminGuard())
	financialDNAGroup.GET("/:id", c.Find(), middlewares.AdminGuard())
	financialDNAGroup.GET("/me", c.FindByUser())
	financialDNAGroup.PUT("/:id", c.Update(), middlewares.AdminGuard())
	financialDNAGroup.DELETE("/:id", c.Delete(), middlewares.AdminGuard())

	// Member CRUD
	membersGroup := financialDNAGroup.Group("/members")
	membersGroup.PATCH("/:memberId", c.PatchMember())
	membersGroup.DELETE("/:memberId", c.DeleteMember(), middlewares.AdminGuard())

	membersGroup.POST("/:parentId/children", c.CreateChild()) // Keep CreateChild relative to parent
	membersGroup.DELETE("/:parentId/children/:childId", c.DeleteChild())

	// Utility
	financialDNAGroup.POST("/initialize", c.Initialize(), middlewares.AdminGuard())
}

// DTOs for request/response bodies
type PatchRequest struct {
	Photo           *multipart.FileHeader `form:"photo,omitempty"`
	Name            string                `form:"name" validate:"required,min=1,max=50"`
	FinancialStatus string                `form:"financialStatus" validate:"required"`
}

// CRUD
func (fc *controller) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		_, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var tree financialdna.FinancialDNATree
		if err := c.Bind(&tree); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		treeID, err := fc.Service.Create(ctx, tree.UserID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, treeID)
	}
}

func (fc *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := c.Param("id")

		tree, err := fc.Service.Find(ctx, id)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tree)
	}
}

func (fc *controller) FindByUser() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		tree, err := fc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Assume that during Financial DNA initialization the first member is the user
		// and the tree is created with the user as the root member.
		me := tree.Members[0].ID
		newTree, err := tree.FormatResponse(me)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, newTree)
	}
}

func (fc *controller) Update() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := c.Param("id")

		var tree financialdna.FinancialDNATree
		if err := c.Bind(&tree); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		tree.ID = id

		if err := fc.Service.Update(ctx, &tree); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tree)
	}
}

func (fc *controller) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := c.Param("id")

		if err := fc.Service.Delete(ctx, id); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// Member CRUD
func (fc *controller) PatchMember() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		memberID := c.Param("memberId")
		if memberID == "" {
			return errors.New(errors.Controller, "member ID is required", errors.Validation, nil)
		}

		var req PatchRequest
		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid request body", errors.Validation, err)
		}
		if err := c.Validate(&req); err != nil {
			return errors.New(errors.Controller, "validation error", errors.Validation, err)
		}

		// TODO IMPROVE THE VALIDATION WITH THE go-playground/validator so I can remove the logic from here
		// Get photo from request
		photo, err := c.FormFile("photo")
		// If a photo is provided, validate it. If no photo, err will be http.ErrMissingFile, which is fine.
		// If there's another error, it's a problem.
		if err != nil && err != http.ErrMissingFile {
			return errors.New(errors.Controller, "failed to process photo", errors.Validation, err)
		}
		if photo != nil {
			// Validate file type
			ext := strings.ToLower(filepath.Ext(photo.Filename))
			allowedExts := map[string]bool{
				".jpg":  true,
				".jpeg": true,
				".png":  true,
				".heic": true,
			}
			if !allowedExts[ext] {
				return errors.New(errors.Controller, "invalid file type, only JPG, JPEG, and PNG are allowed", errors.Validation, nil)
			}

			// Validate file size (max 5MB)
			if photo.Size > 5*1024*1024 {
				return errors.New(errors.Controller, "file too large, max size is 5MB", errors.Validation, nil)
			}
		} else {
			// Explicitly set photo to nil if it's http.ErrMissingFile, so it's not passed to the service.
			// This handles the case where "photo" form field is entirely absent.
			// If c.FormFile returns an error other than http.ErrMissingFile, we would have returned earlier.
			photo = nil
		}

		member := &financialdna.FamilyMember{
			Name:            req.Name,
			FinancialStatus: financialdna.FinancialStatus(req.FinancialStatus),
		}

		// Call the service layer PatchMember
		updatedTree, err := fc.Service.PatchMember(ctx, userToken.Uid, memberID, member, photo)
		if err != nil {
			return err // Handle specific service errors if necessary
		}

		// Return the updated tree or just status OK
		return c.JSON(http.StatusOK, updatedTree)
	}
}

func (fc *controller) DeleteMember() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		memberID := c.Param("memberId")
		if memberID == "" {
			return errors.New(errors.Controller, "member ID is required", errors.Validation, nil)
		}

		tree, err := fc.Service.DeleteMember(ctx, userToken.Uid, memberID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tree)
	}
}

func (fc *controller) CreateChild() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		parentID := c.Param("parentId")
		if parentID == "" {
			return errors.New(errors.Controller, "member ID is required", errors.Validation, nil)
		}

		var child financialdna.FamilyMember
		if err := c.Bind(&child); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		tree, err := fc.Service.CreateChild(ctx, userToken.Uid, parentID, &child)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tree)
	}
}

func (fc *controller) DeleteChild() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		parentID := c.Param("parentId")
		if parentID == "" {
			return errors.New(errors.Controller, "member ID is required", errors.Validation, nil)
		}

		childID := c.Param("childId")
		if childID == "" {
			return errors.New(errors.Controller, "child ID is required", errors.Validation, nil)
		}

		tree, err := fc.Service.DeleteChild(ctx, userToken.Uid, parentID, childID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tree)
	}
}

// Utility
func (fc *controller) Initialize() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var user model.User
		if err := c.Bind(&user); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		err := fc.Service.Initialize(ctx, user.ID)
		if err != nil {
			return err
		}

		tree, err := fc.Service.FindByUser(ctx, user.ID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tree)
	}
}
