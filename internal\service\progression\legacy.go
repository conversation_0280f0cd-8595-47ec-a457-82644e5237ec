package progression

import (
	"context"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"math"
)

// Legacy conversion functions for maintaining backward compatibility with the frontend

// convertSummaryToLegacy converts a ProgressSummary to the legacy Progression format
func (s *service) convertSummaryToLegacy(ctx context.Context, summary *progression.ProgressSummary) (*progression.Progression, error) {
	trails := make([]*progression.Trail, 0, len(summary.Trails))

	for _, trailSummary := range summary.Trails {
		// Get trail and lesson content for proper availability calculation
		trailContent, err := s.TrailService.Find(ctx, trailSummary.ID)
		if err != nil {
			return nil, err
		}

		legacyTrail := ConvertTrailSummaryToLegacy(trailSummary, trailContent)
		trails = append(trails, legacyTrail)
	}

	return &progression.Progression{
		User:      summary.UserID,
		Trails:    trails,
		CreatedAt: summary.UpdatedAt,
		UpdatedAt: summary.UpdatedAt,
	}, nil
}

// ConvertTrailSummaryToLegacy converts a TrailSummary to the legacy Trail format
func ConvertTrailSummaryToLegacy(summary *progression.TrailSummary, trailContent *content.Trail) *progression.Trail {
	lessons := make([]*progression.Lesson, 0, len(summary.LessonProgress))

	for lessonID, progress := range summary.LessonProgress {
		legacyLesson := convertLessonProgressToLegacy(progress)
		legacyLesson.Identifier = lessonID
		lessons = append(lessons, legacyLesson)
	}

	var challenge *progression.Challenge
	if summary.ChallengeProgress != nil {
		challenge = convertChallengeProgressToLegacy(summary.ChallengeProgress)
		challenge.Identifier = trailContent.Challenge.Identifier
	}

	var progressionPercent uint8
	if summary.ProgressPercent > math.MaxUint8 {
		progressionPercent = 100
	} else if summary.ProgressPercent < 0 {
		progressionPercent = 0
	} else {
		progressionPercent = uint8(summary.ProgressPercent)
	}

	return &progression.Trail{
		ID:                 summary.ID,
		Total:              progressionPercent,
		Available:          true, // Trails are always available once started
		LessonsCompleted:   len(summary.LessonsCompleted) == len(trailContent.Lessons),
		ChallengeCompleted: summary.IsCompleted,
		Lessons:            lessons,
		Challenge:          challenge,
	}
}

// convertLessonProgressToLegacy converts a LessonProgress to the legacy Lesson format
func convertLessonProgressToLegacy(progress *progression.LessonProgress) *progression.Lesson {
	// This conversion does not maintain the choice selection on path since this is created from summary and not events.
	path := make([]*progression.LessonContent, 0)

	return &progression.Lesson{
		Identifier: "", // Set by caller
		Current:    progress.Current,
		Completed:  progress.Completed,
		Available:  true, // Will be calculated based on requirements when needed
		Rewarded:   progress.Rewarded,
		Path:       path,
	}
}

// convertChallengeProgressToLegacy converts a ChallengeProgress to the legacy Challenge format
func convertChallengeProgressToLegacy(progress *progression.ChallengeProgress) *progression.Challenge {
	phases := make([]*progression.ChallengePhase, 0, len(progress.Phases))

	for phaseID, phase := range progress.Phases {
		legacyPhase := &progression.ChallengePhase{
			Identifier: phaseID,
			Current:    phase.Current,
			Completed:  phase.Completed,
			Available:  true,                                     // Will be calculated based on requirements when needed
			Path:       make([]*progression.ChallengeContent, 0), // Empty path for cards
		}
		phases = append(phases, legacyPhase)
	}

	// Calculate total progress based on completed phases
	var intermediateValue int
	if len(phases) > 0 {
		completedPhases := 0
		for _, phase := range phases {
			if phase.Completed {
				completedPhases++
			}
		}
		// Safe calculation to prevent integer overflow
		if completedPhases > 0 && len(phases) > 0 {
			// Use float64 for calculation to avoid overflow, then clamp result
			percentage := float64(completedPhases) * 100.0 / float64(len(phases))
			if percentage > float64(math.MaxUint8) {
				intermediateValue = math.MaxUint8
			} else {
				intermediateValue = int(percentage)
			}
		}
	}

	var total uint8
	if intermediateValue > math.MaxUint8 {
		total = math.MaxUint8
	} else if intermediateValue < 0 {
		total = 0
	} else {
		total = uint8(intermediateValue)
	}

	return &progression.Challenge{
		Identifier: "", // Set by caller
		Current:    progress.Current,
		Completed:  progress.Completed,
		Available:  true, // Will be calculated based on requirements when needed
		Rewarded:   progress.Rewarded,
		Phases:     phases,
		Total:      total,
	}
}

// ConvertLessonProgressToLegacyWithContent converts lesson progress with proper availability calculation
func ConvertLessonProgressToLegacyWithContent(
	lessonID string,
	trailSummary *progression.TrailSummary,
	lessonContent *content.Lesson,
) *progression.Lesson {
	// Get lesson progress from summary
	lessonProgress := trailSummary.LessonProgress[lessonID]

	var completed, rewarded bool
	var current string
	var available = true

	if lessonProgress != nil {
		completed = lessonProgress.Completed
		rewarded = lessonProgress.Rewarded
		current = lessonProgress.Current
	} else {
		// Calculate availability based on lesson requirements
		available = calculateLessonAvailability(lessonContent, trailSummary)
	}

	return &progression.Lesson{
		Identifier: lessonID,
		Current:    current,
		Completed:  completed,
		Available:  available,
		Rewarded:   rewarded,
		Path:       make([]*progression.LessonContent, 0), // Empty path for cards/API responses
	}
}

// ConvertChallengeProgressToLegacyWithContent converts challenge progress with proper availability calculation

// ConvertChallengeProgressToLegacyWithContent converts challenge progress with proper availability calculation
func ConvertChallengeProgressToLegacyWithContent(
	challengeID string,
	trailSummary *progression.TrailSummary,
	trailContent *content.Trail,
) *progression.Challenge {
	// Get challenge progress from summary
	challengeContent := trailContent.Challenge
	challengeProgress := trailSummary.ChallengeProgress
	var completed, rewarded bool
	var current string
	var available bool
	var phases []*progression.ChallengePhase
	var intermediateValue int
	if challengeProgress != nil {
		completed = challengeProgress.Completed
		rewarded = challengeProgress.Rewarded
		current = challengeProgress.Current
		available = true
		// Convert phases
		phases = make([]*progression.ChallengePhase, 0, len(challengeProgress.Phases))
		for phaseID, phase := range challengeProgress.Phases {
			phaseContent := challengeContent.GetPhase(phaseID)
			legacyPhase := &progression.ChallengePhase{
				Identifier:    phaseID,
				Current:       phase.Current,
				Completed:     phase.Completed,
				CurrentPoints: phase.CurrentPoints,
				Available:     calculatePhaseAvailability(phaseContent, trailSummary),
				Path:          make([]*progression.ChallengeContent, 0), // Empty path for cards
			}
			phases = append(phases, legacyPhase)
		}
		// Calculate total progress based on completed phases
		if len(phases) > 0 {
			completedPhases := 0
			for _, phase := range phases {
				if phase.Completed {
					completedPhases++
				}
			}
			// Safe calculation to prevent integer overflow
			if completedPhases > 0 && len(phases) > 0 {
				// Use float64 for calculation to avoid overflow, then clamp result
				percentage := float64(completedPhases) * 100.0 / float64(len(phases))
				if percentage > float64(math.MaxUint8) {
					intermediateValue = math.MaxUint8
				} else {
					intermediateValue = int(percentage)
				}
			}
		}
	} else {
		// Calculate availability - challenge is available when all lessons are completed
		available = calculateChallengeAvailability(challengeContent, trailContent, trailSummary)
		phases = make([]*progression.ChallengePhase, 0)
	}
	var total uint8
	if intermediateValue > math.MaxUint8 {
		total = math.MaxUint8
	} else if intermediateValue < 0 {
		total = 0
	} else {
		total = uint8(intermediateValue)
	}
	return &progression.Challenge{
		Identifier: challengeID,
		Current:    current,
		Completed:  completed,
		Available:  available,
		Rewarded:   rewarded,
		Phases:     phases,
		Total:      total,
	}
}

// calculateLessonAvailability calculates if a lesson should be available based on requirements
func calculateLessonAvailability(lessonContent *content.Lesson, trailSummary *progression.TrailSummary) bool {
	if lessonContent == nil || len(lessonContent.Requirements) == 0 {
		return true // No requirements, always available
	}

	// Check if all required lessons are completed
	for _, requirement := range lessonContent.Requirements {
		if lessonProgress, exists := trailSummary.LessonProgress[requirement]; !exists || !lessonProgress.Completed {
			return false
		}
	}

	return true
}

// calculateChallengeAvailability calculates if a challenge should be available
func calculateChallengeAvailability(challengeContent *content.Challenge, trailContent *content.Trail, trailSummary *progression.TrailSummary) bool {
	if challengeContent == nil {
		return false
	}

	// If challenge is locked, it's not available
	if challengeContent.Locked {
		return false
	}

	// Check if all lessons in the trail are completed
	for _, lesson := range trailContent.Lessons {
		if lessonProgress, exists := trailSummary.LessonProgress[lesson.Identifier]; !exists || !lessonProgress.Completed {
			return false
		}
	}

	return true
}

// calculatePhaseAvailability calculates if a challenge phase should be available
func calculatePhaseAvailability(phaseContent *content.ChallengePhase, trailSummary *progression.TrailSummary) bool {
	if phaseContent == nil || len(phaseContent.Requirements) == 0 {
		return true // No requirements, always available
	}

	// Get challenge progress
	challengeProgress := trailSummary.ChallengeProgress
	if challengeProgress == nil {
		return false // Challenge not started, phase not available
	}

	// Check if all required phases or lessons are completed
	for _, requirement := range phaseContent.Requirements {
		// First check if requirement is a phase
		if phaseProgress, exists := challengeProgress.Phases[requirement]; exists {
			if !phaseProgress.Completed {
				return false
			}
		} else {
			// Check if requirement is a lesson
			if lessonProgress, exists := trailSummary.LessonProgress[requirement]; !exists || !lessonProgress.Completed {
				return false
			}
		}
	}

	return true
}
