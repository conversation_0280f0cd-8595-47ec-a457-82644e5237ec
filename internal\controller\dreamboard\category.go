package dreamboard

import (
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/labstack/echo/v4"
)

// Category CRUD
func (dc *controller) CreateCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "boardId")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		board, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		// Authorization: Only admins or the dreamboard owner can create categories
		if userToken.Role != "admin" && userToken.Uid != board.User {
			return errors.NewWithTranslationKey(errors.Controller, "you can only manage categories in your own dreamboard", errors.KeyDreamboardErrorUnauthorized, errors.Forbidden, nil)
		}

		var category dreamboard.Category
		if err := c.Bind(&category); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid input", errors.KeyDreamboardErrorInvalidInput, err)
		}

		createdCategory, err := dc.Service.CreateCategory(ctx, board, &category)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, createdCategory)
	}
}

func (dc *controller) FindCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "boardId")
		if err != nil {
			return err
		}

		categoryID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		board, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		category, err := dc.Service.FindCategory(ctx, board, categoryID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, category)
	}
}

func (dc *controller) UpdateCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "boardId")
		if err != nil {
			return err
		}

		categoryID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		board, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		// Authorization: Only admins or the dreamboard owner can update categories
		if userToken.Role != "admin" && userToken.Uid != board.User {
			return errors.NewWithTranslationKey(errors.Controller, "you can only manage categories in your own dreamboard", errors.KeyDreamboardErrorUnauthorized, errors.Forbidden, nil)
		}

		var category dreamboard.Category
		if err := c.Bind(&category); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid input", errors.KeyDreamboardErrorInvalidInput, err)
		}
		category.ID = categoryID

		updatedCategory, err := dc.Service.UpdateCategory(ctx, board, &category)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedCategory)
	}
}

func (dc *controller) DeleteCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "boardId")
		if err != nil {
			return err
		}

		categoryID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		board, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		// Authorization: Only admins or the dreamboard owner can delete categories
		if userToken.Role != "admin" && userToken.Uid != board.User {
			return errors.NewWithTranslationKey(errors.Controller, "you can only manage categories in your own dreamboard", errors.KeyDreamboardErrorUnauthorized, errors.Forbidden, nil)
		}

		if err := dc.Service.DeleteCategory(ctx, board, categoryID); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}
