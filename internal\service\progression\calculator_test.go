package progression

import (
	"context"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"testing"
	"time"
)

func TestCalculator_CalculateProgressFromEvents(t *testing.T) {
	mockTrailSvc := new(MockTrailSvc)
	calculator := &Calculator{
		trailService: mockTrailSvc,
	}

	userID := "user1"
	trailID := "trail1"
	lessonID := "lesson1"
	firstContent := "content1"
	secondContent := "content2"
	firstChoice := "choice1"
	challengeID := "challenge1"

	mockTrail := &content.Trail{
		ID: trailID,
		Lessons: []*content.Lesson{
			{Identifier: lessonID},
		},
		Challenge: &content.Challenge{
			Identifier: challengeID,
		},
	}

	mockTrailSvc.On("Find", mock.Anything, trailID).Return(mockTrail, nil)

	events := []*progression.ProgressEvent{
		{
			UserID:    userID,
			TrailID:   trailID,
			ItemID:    lessonID,
			ContentID: firstContent,
			ItemType:  progression.LessonProgressType,
			Action:    progression.StartedActionType,
			Timestamp: time.Now().Add(-1 * time.Hour),
		},
		{
			UserID:    userID,
			TrailID:   trailID,
			ItemID:    lessonID,
			ContentID: secondContent,
			ItemType:  progression.LessonProgressType,
			Action:    progression.CompletedActionType,
			Timestamp: time.Now(),
			Choice: &progression.Choice{
				Identifier: firstChoice,
				Next:       string(progression.RewardTypeCoin),
			},
		},
	}

	summary, err := calculator.CalculateProgressFromEvents(context.Background(), events, userID)

	assert.NoError(t, err)
	assert.NotNil(t, summary)
	assert.Equal(t, userID, summary.UserID)
	assert.Contains(t, summary.Trails, trailID)

	trailSummary := summary.Trails[trailID]
	assert.True(t, trailSummary.LessonsCompleted[lessonID])
	assert.True(t, trailSummary.LessonProgress[lessonID].Completed)
	assert.True(t, trailSummary.LessonProgress[lessonID].Rewarded)
	assert.Equal(t, 1, trailSummary.TotalRewards)
}
