package league

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
)

// FindLeagueCard returns a simplified card representation of a league
func (s *service) FindLeagueCard(ctx context.Context, leagueID string, userID string, userTimezone *time.Location) (*league.LeagueCard, error) {
	if leagueID == "" {
		return nil, errors.New(errors.Service, "league_id_required", errors.Validation, nil)
	}
	if userID == "" {
		return nil, errors.New(errors.Service, "user_id_required", errors.Validation, nil)
	}

	// Get the league with access control
	l, err := s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
	if err != nil {
		return nil, err
	}

	// Convert to card
	return s.leagueToCard(l, userID, userTimezone), nil
}

// FindAllLeaguesCards returns simplified card representations of all leagues a user is a member of
func (s *service) FindAllLeaguesCards(ctx context.Context, userID string, userTimezone *time.Location) ([]*league.LeagueCard, error) {
	if userID == "" {
		return nil, errors.New(errors.Service, "user_id_required", errors.Validation, nil)
	}

	// Get all leagues the user is a member of
	leagues, err := s.Repository.FindAllLeagues(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Convert each league to a card
	cards := make([]*league.LeagueCard, 0, len(leagues))
	for _, l := range leagues {
		cards = append(cards, s.leagueToCard(l, userID, userTimezone))
	}

	return cards, nil
}

// Helper
// leagueToCard converts a League to a LeagueCard
func (s *service) leagueToCard(l *league.League, userID string, userTimezone *time.Location) *league.LeagueCard {
	// Find the leader (member with highest transaction streak)
	var leader *league.LeagueMember
	var userMember *league.LeagueMember

	// Find the user and leader
	for i, member := range l.Members {
		// Keep a reference to the user's member data
		if member.UserID == userID {
			userMember = &l.Members[i]
		}

		// Find the leader (member with highest transaction streak)
		if leader == nil || member.TransactionStreak > leader.TransactionStreak {
			leader = &l.Members[i]
		}
	}

	// Create the card
	card := &league.LeagueCard{
		ObjectID:              l.ObjectID,
		ID:                    l.ID,
		Name:                  l.Name,
		BackgroundColor:       l.BackgroundColor,
		RemainingDaysInSeason: calculateRemainingDaysInSeason(l.CurrentSeason.EndDate, userTimezone),
	}

	// Set leader information if found
	if leader != nil {
		card.LeaderName = leader.UserName
		card.LeaderPhotoURL = leader.PhotoURL
		card.LeaderTransactionStreak = leader.TransactionStreak
	}

	// Set user information if found
	if userMember != nil {
		card.UserName = userMember.UserName
		card.UserPhotoURL = userMember.PhotoURL
		card.UserTransactionStreak = userMember.TransactionStreak
	}

	return card
}

// calculateRemainingDaysInSeason calculates the number of calendar days remaining in a season
// from the user's perspective.
func calculateRemainingDaysInSeason(seasonEndDate time.Time, userTimezone *time.Location) int {
	// 1. Determine the user's current calendar day as a truncated UTC date.
	nowInUserTZ := time.Now().In(userTimezone)
	todayUTC := time.Date(
		nowInUserTZ.Year(),
		nowInUserTZ.Month(),
		nowInUserTZ.Day(),
		0, 0, 0, 0, time.UTC,
	)

	// 2. Determine the season's last calendar day.
	// Since seasonEndDate is the exclusive boundary (e.g., Oct 28 00:00 for a season ending Oct 27),
	// we subtract one day to get the actual last day of the season.
	lastDayOfSeasonUTC := seasonEndDate.AddDate(0, 0, -1)

	// 3. Check if the season is already over from the user's perspective.
	if todayUTC.After(lastDayOfSeasonUTC) {
		return 0
	}

	// 4. Calculate the difference in days.
	// Because both are truncated UTC dates, this calculation is safe and avoids DST issues.
	duration := lastDayOfSeasonUTC.Sub(todayUTC)

	// The result of the subtraction gives us the number of full 24-hour periods.
	// We add 1 to make the count inclusive (i.e., to include today).
	// Example: If today is the last day, duration is 0, but there is still 1 day remaining.
	remainingDays := int(duration.Hours()/24) + 1

	return remainingDays
}
