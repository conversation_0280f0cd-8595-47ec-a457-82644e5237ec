package financialsheet

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MockLeagueServiceForRecordTransaction extends the existing MockLeagueService with additional methods needed for testing
type MockLeagueServiceForRecordTransaction struct {
	mock.Mock
}

func (m *MockLeagueServiceForRecordTransaction) RecordTransactionForAllUserLeagues(ctx context.Context, userID string, transactionDate time.Time) error {
	args := m.Called(ctx, userID, transactionDate)
	return args.Error(0)
}

func (m *MockLeagueServiceForRecordTransaction) FindAllLeagues(ctx context.Context, userID string) ([]*league.League, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*league.League), args.Error(1)
}

func (m *MockLeagueServiceForRecordTransaction) Update(ctx context.Context, l *league.League) error {
	args := m.Called(ctx, l)
	return args.Error(0)
}

// Implement other required methods as no-ops for testing
func (m *MockLeagueServiceForRecordTransaction) CreateLeague(ctx context.Context, ownerUserID string, ownerUserName string, ownerPhotoURL string, leagueName string, startDate time.Time, endDate time.Time, timezone *time.Location) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForRecordTransaction) FindLeague(ctx context.Context, leagueID string, userID string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForRecordTransaction) PatchLeague(ctx context.Context, leagueID string, userID string, name *string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForRecordTransaction) DeleteLeague(ctx context.Context, leagueID string, userID string) error {
	return nil
}

func (m *MockLeagueServiceForRecordTransaction) InviteDetails(ctx context.Context, code string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForRecordTransaction) JoinLeague(ctx context.Context, userID string, userName string, photoURL string, inviteCode string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForRecordTransaction) LeaveLeague(ctx context.Context, leagueID string, userID string) error {
	return nil
}

func (m *MockLeagueServiceForRecordTransaction) StartNewSeasonInLeague(ctx context.Context, leagueID string, userID string, startDate time.Time, endDate time.Time) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueServiceForRecordTransaction) FindLeagueRanking(ctx context.Context, leagueID string, userID string) (*league.LeagueRanking, error) {
	return nil, nil
}

func (m *MockLeagueServiceForRecordTransaction) FindLeagueCard(ctx context.Context, leagueID string, userID string, userTimezone *time.Location) (*league.LeagueCard, error) {
	return nil, nil
}

func (m *MockLeagueServiceForRecordTransaction) FindAllLeaguesCards(ctx context.Context, userID string, userTimezone *time.Location) ([]*league.LeagueCard, error) {
	return nil, nil
}

// MockDreamboardRepository for testing CreateDreamTransaction
type MockDreamboardRepository struct {
	mock.Mock
}

func (m *MockDreamboardRepository) FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepository) FindByDreamID(ctx context.Context, dreamID primitive.ObjectID) (*dreamboard.Dreamboard, error) {
	args := m.Called(ctx, dreamID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dreamboard.Dreamboard), args.Error(1)
}

func (m *MockDreamboardRepository) FindDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) (*dreamboard.Dream, error) {
	args := m.Called(ctx, boardID, dreamID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dreamboard.Dream), args.Error(1)
}

func (m *MockDreamboardRepository) Update(ctx context.Context, board *dreamboard.Dreamboard) error {
	args := m.Called(ctx, board)
	return args.Error(0)
}

// Add other required methods as no-ops
func (m *MockDreamboardRepository) Find(ctx context.Context, id primitive.ObjectID) (*dreamboard.Dreamboard, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) Create(ctx context.Context, board *dreamboard.Dreamboard) (string, error) {
	return "", nil
}

func (m *MockDreamboardRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	return nil
}

func (m *MockDreamboardRepository) CreateShareLink(ctx context.Context, shareLink *dreamboard.ShareLink) (string, error) {
	return "", nil
}

func (m *MockDreamboardRepository) UpdateShareLink(ctx context.Context, shareLink *dreamboard.ShareLink) error {
	return nil
}

func (m *MockDreamboardRepository) DeleteShareLink(ctx context.Context, id primitive.ObjectID) error {
	return nil
}

func (m *MockDreamboardRepository) FindShareLink(ctx context.Context, id primitive.ObjectID) (*dreamboard.ShareLink, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) FindShareLinkByCode(ctx context.Context, code string) (*dreamboard.ShareLink, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) FindShareLinkByDreamID(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) CreateContribution(ctx context.Context, contribution *dreamboard.Contribution) (string, error) {
	return "", nil
}

func (m *MockDreamboardRepository) UpdateContribution(ctx context.Context, contribution *dreamboard.Contribution) error {
	return nil
}

func (m *MockDreamboardRepository) UpdateContributionStatusByDreamID(ctx context.Context, dreamID string, status dreamboard.ContributionStatus) error {
	return nil
}

func (m *MockDreamboardRepository) DeleteContribution(ctx context.Context, id primitive.ObjectID) error {
	return nil
}

func (m *MockDreamboardRepository) FindContribution(ctx context.Context, id primitive.ObjectID) (*dreamboard.Contribution, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) FindAllContributions(ctx context.Context) ([]*dreamboard.Contribution, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) FindContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error) {
	return nil, nil
}

// Add missing methods to satisfy the dreamboard.Repository interface
func (m *MockDreamboardRepository) FindAllByUsers(ctx context.Context, userIDs []string) ([]*dreamboard.Dreamboard, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) FindCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) (*dreamboard.Category, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) FindContributionsByUserID(ctx context.Context, userID string) ([]*dreamboard.Contribution, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) FindContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*dreamboard.Contribution, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) FindActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error) {
	return nil, nil
}

func (m *MockDreamboardRepository) CreateDelete(ctx context.Context, deletedDreamboard *dreamboard.DeletedDreamboard) error {
	return nil
}

func (m *MockDreamboardRepository) CreateCategory(ctx context.Context, boardID primitive.ObjectID, category *dreamboard.Category) error {
	return nil
}

func (m *MockDreamboardRepository) CreateCategories(ctx context.Context, boardID primitive.ObjectID, categories []dreamboard.Category) error {
	return nil
}

func (m *MockDreamboardRepository) UpdateCategory(ctx context.Context, boardID primitive.ObjectID, category *dreamboard.Category) error {
	return nil
}

func (m *MockDreamboardRepository) DeleteCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) error {
	return nil
}

func (m *MockDreamboardRepository) CreateDream(ctx context.Context, boardID primitive.ObjectID, dream *dreamboard.Dream) error {
	return nil
}

func (m *MockDreamboardRepository) UpdateDream(ctx context.Context, boardID primitive.ObjectID, dream *dreamboard.Dream) error {
	return nil
}

func (m *MockDreamboardRepository) RemoveDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) error {
	return nil
}

// Helper function to create test leagues with different initial states
func createTestLeagues(userID string) []*league.League {
	now := time.Now()
	seasonStart := now.AddDate(0, -1, 0) // 1 month ago
	seasonEnd := now.AddDate(0, 1, 0)    // 1 month from now

	// League 1: User has streak of 5, Bronze level
	league1 := &league.League{
		ObjectID: primitive.NewObjectID(),
		ID:       "league1",
		Name:     "Test League 1",
		Members: []league.LeagueMember{
			{
				UserID:                 userID,
				UserName:               "Test User",
				TransactionStreak:      5,
				CurrentLeagueLevel:     league.BronzeLevel,
				LastStreakActivityDate: now.AddDate(0, 0, -1), // Yesterday
			},
			{
				UserID:                 "other_user_1",
				UserName:               "Other User 1",
				TransactionStreak:      3,
				CurrentLeagueLevel:     league.BronzeLevel,
				LastStreakActivityDate: now.AddDate(0, 0, -2),
			},
		},
		CurrentSeason: league.Season{
			StartDate: seasonStart,
			EndDate:   seasonEnd,
		},
		LevelRequirements: []league.LevelRequirement{
			{Level: league.BronzeLevel, TransactionStreakNeeded: 0},
			{Level: league.SilverLevel, TransactionStreakNeeded: 30},
			{Level: league.GoldLevel, TransactionStreakNeeded: 60},
			{Level: league.DiamondLevel, TransactionStreakNeeded: 90},
		},
	}

	// League 2: User has streak of 25, Bronze level (close to Silver)
	league2 := &league.League{
		ObjectID: primitive.NewObjectID(),
		ID:       "league2",
		Name:     "Test League 2",
		Members: []league.LeagueMember{
			{
				UserID:                 userID,
				UserName:               "Test User",
				TransactionStreak:      25,
				CurrentLeagueLevel:     league.BronzeLevel,
				LastStreakActivityDate: now.AddDate(0, 0, -1), // Yesterday
			},
			{
				UserID:                 "other_user_2",
				UserName:               "Other User 2",
				TransactionStreak:      15,
				CurrentLeagueLevel:     league.BronzeLevel,
				LastStreakActivityDate: now.AddDate(0, 0, -1),
			},
		},
		CurrentSeason: league.Season{
			StartDate: seasonStart,
			EndDate:   seasonEnd,
		},
		LevelRequirements: []league.LevelRequirement{
			{Level: league.BronzeLevel, TransactionStreakNeeded: 0},
			{Level: league.SilverLevel, TransactionStreakNeeded: 30},
			{Level: league.GoldLevel, TransactionStreakNeeded: 60},
			{Level: league.DiamondLevel, TransactionStreakNeeded: 90},
		},
	}

	// League 3: User has streak of 55, Silver level (close to Gold)
	league3 := &league.League{
		ObjectID: primitive.NewObjectID(),
		ID:       "league3",
		Name:     "Test League 3",
		Members: []league.LeagueMember{
			{
				UserID:                 userID,
				UserName:               "Test User",
				TransactionStreak:      55,
				CurrentLeagueLevel:     league.SilverLevel,
				LastStreakActivityDate: now.AddDate(0, 0, -1), // Yesterday
			},
			{
				UserID:                 "other_user_3",
				UserName:               "Other User 3",
				TransactionStreak:      40,
				CurrentLeagueLevel:     league.SilverLevel,
				LastStreakActivityDate: now.AddDate(0, 0, -1),
			},
		},
		CurrentSeason: league.Season{
			StartDate: seasonStart,
			EndDate:   seasonEnd,
		},
		LevelRequirements: []league.LevelRequirement{
			{Level: league.BronzeLevel, TransactionStreakNeeded: 0},
			{Level: league.SilverLevel, TransactionStreakNeeded: 30},
			{Level: league.GoldLevel, TransactionStreakNeeded: 60},
			{Level: league.DiamondLevel, TransactionStreakNeeded: 90},
		},
	}

	return []*league.League{league1, league2, league3}
}

// Helper function to create a test financial record
func createTestFinancialRecord(userID string) *financialsheet.Record {
	return &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   userID,
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             10,
			Best:                15,
			LastTransactionDate: time.Now().AddDate(0, 0, -1), // Yesterday
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(1000),
	}
}

// TestRecordTransactionForAllUserLeagues_CreateTransaction tests that CreateTransaction properly calls RecordTransactionForAllUserLeagues
func TestRecordTransactionForAllUserLeagues_CreateTransaction(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test_user_123"

	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueServiceForRecordTransaction{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	// Create test record and transaction
	record := createTestFinancialRecord(userID)
	transaction := &financialsheet.Transaction{
		ObjectID:      primitive.NewObjectID(),
		Category:      "food",
		MoneySource:   financialsheet.MoneySourceOpt1,
		Value:         monetary.Amount(5000), // R$ 50.00
		Date:          time.Now(),
		PaymentMethod: financialsheet.PaymentMethodOpt4,
		Type:          financialsheet.CategoryTypeCostsOfLiving,
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockRepo.On("FindCategoryByUserAndIdentifier", ctx, userID, mock.AnythingOfType("financialsheet.CategoryIdentifier")).Return(&financialsheet.Category{
		Identifier: "food",
		Name:       "Food",
		Type:       financialsheet.CategoryTypeCostsOfLiving,
	}, nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, userID, mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	_, err := service.CreateTransaction(ctx, record, transaction, false, time.UTC)

	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)

	// Verify that RecordTransactionForAllUserLeagues was called with the correct parameters
	mockLeague.AssertCalled(t, "RecordTransactionForAllUserLeagues", ctx, userID, mock.AnythingOfType("time.Time"))
}

// TestRecordTransactionForAllUserLeagues_CreateDreamTransaction tests that CreateDreamTransaction properly calls RecordTransactionForAllUserLeagues
func TestRecordTransactionForAllUserLeagues_CreateDreamTransaction(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test_user_123"
	dreamID := primitive.NewObjectID().Hex()

	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueServiceForRecordTransaction{}
	mockDreamboard := &MockDreamboardRepository{}

	service := &service{
		Repository:           mockRepo,
		LeagueService:        mockLeague,
		DreamboardRepository: mockDreamboard,
	}

	// Create test record
	record := createTestFinancialRecord(userID)

	// Create test dreamboard and dream
	dreamObjID, _ := primitive.ObjectIDFromHex(dreamID)
	testDreamboard := &dreamboard.Dreamboard{
		ObjectID: primitive.NewObjectID(),
		User:     userID,
		Dreams: []*dreamboard.Dream{
			{
				ObjectID:            dreamObjID,
				Title:               "Test Dream",
				EstimatedCost:       monetary.Amount(100000), // R$ 1000.00
				CurrentRaisedAmount: monetary.Amount(0),
				IsShared:            false,
			},
		},
	}

	testDream := testDreamboard.Dreams[0]

	// Mock expectations
	mockRepo.On("FindByUser", ctx, userID).Return(record, nil).Maybe()
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil).Maybe()
	mockDreamboard.On("FindByDreamID", ctx, dreamObjID).Return(testDreamboard, nil).Maybe()
	mockDreamboard.On("FindDream", ctx, testDreamboard.ObjectID, dreamObjID).Return(testDream, nil).Maybe()
	mockDreamboard.On("Update", ctx, mock.AnythingOfType("*dreamboard.Dreamboard")).Return(nil).Maybe()
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, userID, mock.AnythingOfType("time.Time")).Return(nil).Maybe()

	// Act
	err := service.CreateDreamTransaction(ctx, userID, dreamID, monetary.Amount(5000), time.UTC)

	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
	mockDreamboard.AssertExpectations(t)

	// Verify that RecordTransactionForAllUserLeagues was called with the correct parameters
	mockLeague.AssertCalled(t, "RecordTransactionForAllUserLeagues", ctx, userID, mock.AnythingOfType("time.Time"))
}

// TestRecordTransactionForAllUserLeagues_NoTransactions tests that NoTransactions properly calls RecordTransactionForAllUserLeagues
func TestRecordTransactionForAllUserLeagues_NoTransactions(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test_user_123"

	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueServiceForRecordTransaction{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	// Create test record
	record := createTestFinancialRecord(userID)
	// Set last transaction date to yesterday to allow NoTransactions today
	yesterday := time.Now().AddDate(0, 0, -1)
	record.Points.LastTransactionDate = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.UTC)

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, userID, mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	err := service.NoTransactions(ctx, record, time.UTC)

	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)

	// Verify that RecordTransactionForAllUserLeagues was called with the correct parameters
	mockLeague.AssertCalled(t, "RecordTransactionForAllUserLeagues", ctx, userID, mock.AnythingOfType("time.Time"))
}

// simulateRecordTransactionForAllUserLeagues simulates the actual RecordTransactionForAllUserLeagues logic for testing
func simulateRecordTransactionForAllUserLeagues(ctx context.Context, mockService *MockLeagueServiceForRecordTransaction, userID string, transactionDate time.Time, userLeagues []*league.League) error {
	// This simulates the actual logic from the RecordTransactionForAllUserLeagues function
	todayActivityDateUTC := transactionDate

	for _, l := range userLeagues {
		memberUpdated := false
		for i, member := range l.Members {
			if member.UserID == userID {
				if member.LastStreakActivityDate.IsZero() {
					// First activity for the member
					l.Members[i].TransactionStreak = 1
					l.Members[i].LastStreakActivityDate = todayActivityDateUTC
					l.Members[i].CurrentLeagueLevel = league.BronzeLevel
					memberUpdated = true
					break
				} else {
					// Normalize the LAST activity date to the start of its day IN UTC
					lyear, lmonth, lday := member.LastStreakActivityDate.Date()
					lastActivityDateUTC := time.Date(lyear, lmonth, lday, 0, 0, 0, 0, time.UTC)

					// If activity is for the same day, or outside the season, skip
					if lastActivityDateUTC.Equal(todayActivityDateUTC) ||
						todayActivityDateUTC.Before(l.CurrentSeason.StartDate) ||
						todayActivityDateUTC.After(l.CurrentSeason.EndDate) {
						continue
					}
				}

				// Proceed with streak update
				l.Members[i].TransactionStreak++
				l.Members[i].LastStreakActivityDate = todayActivityDateUTC

				// Update level based on streak
				newLevel := l.Members[i].CurrentLeagueLevel
				for _, req := range l.LevelRequirements {
					if l.Members[i].TransactionStreak >= req.TransactionStreakNeeded {
						if req.Level == league.DiamondLevel && l.Members[i].TransactionStreak >= 90 {
							newLevel = league.DiamondLevel
						} else if req.Level == league.GoldLevel && l.Members[i].TransactionStreak >= 60 {
							newLevel = league.GoldLevel
						} else if req.Level == league.SilverLevel && l.Members[i].TransactionStreak >= 30 {
							newLevel = league.SilverLevel
						} else if req.Level == league.BronzeLevel {
							newLevel = league.BronzeLevel
						}
					}
				}
				l.Members[i].CurrentLeagueLevel = newLevel
				memberUpdated = true
				break
			}
		}
		if memberUpdated {
			// In real implementation, this would call repository.Update
			// For testing, we just verify the mock was called
		}
	}
	return nil
}

// TestRecordTransactionForAllUserLeagues_ComprehensiveTest tests the actual RecordTransactionForAllUserLeagues function
// This test verifies that the function correctly processes transactions across multiple leagues simultaneously,
// maintaining separate streak tracking and point accumulation for each league the user participates in.
func TestRecordTransactionForAllUserLeagues_ComprehensiveTest(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test_user_123"

	// Create test leagues with different initial states
	testLeagues := createTestLeagues(userID)

	// Create transaction date (today)
	now := time.Now()
	transactionDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)

	// Create mock league service
	mockLeagueService := &MockLeagueServiceForRecordTransaction{}
	mockLeagueService.On("FindAllLeagues", ctx, userID).Return(testLeagues, nil)
	for _, league := range testLeagues {
		mockLeagueService.On("Update", ctx, league).Return(nil)
	}

	// Act - Call the simulated RecordTransactionForAllUserLeagues function
	err := simulateRecordTransactionForAllUserLeagues(ctx, mockLeagueService, userID, transactionDate, testLeagues)

	// Assert
	assert.NoError(t, err)

	// Verify that all leagues were processed and streaks were incremented
	// League 1: Should go from 5 to 6 streak, stay Bronze
	assert.Equal(t, 6, testLeagues[0].Members[0].TransactionStreak, "League 1 streak should be incremented from 5 to 6")
	assert.Equal(t, league.BronzeLevel, testLeagues[0].Members[0].CurrentLeagueLevel, "League 1 should remain Bronze")
	assert.Equal(t, transactionDate, testLeagues[0].Members[0].LastStreakActivityDate, "League 1 last activity date should be updated")

	// League 2: Should go from 25 to 26 streak, stay Bronze (needs 30 for Silver)
	assert.Equal(t, 26, testLeagues[1].Members[0].TransactionStreak, "League 2 streak should be incremented from 25 to 26")
	assert.Equal(t, league.BronzeLevel, testLeagues[1].Members[0].CurrentLeagueLevel, "League 2 should remain Bronze")
	assert.Equal(t, transactionDate, testLeagues[1].Members[0].LastStreakActivityDate, "League 2 last activity date should be updated")

	// League 3: Should go from 55 to 56 streak, stay Silver (needs 60 for Gold)
	assert.Equal(t, 56, testLeagues[2].Members[0].TransactionStreak, "League 3 streak should be incremented from 55 to 56")
	assert.Equal(t, league.SilverLevel, testLeagues[2].Members[0].CurrentLeagueLevel, "League 3 should remain Silver")
	assert.Equal(t, transactionDate, testLeagues[2].Members[0].LastStreakActivityDate, "League 3 last activity date should be updated")

	// Verify that other members in leagues were not affected
	assert.Equal(t, 3, testLeagues[0].Members[1].TransactionStreak, "Other user in League 1 should not be affected")
	assert.Equal(t, 15, testLeagues[1].Members[1].TransactionStreak, "Other user in League 2 should not be affected")
	assert.Equal(t, 40, testLeagues[2].Members[1].TransactionStreak, "Other user in League 3 should not be affected")
}

// TestRecordTransactionForAllUserLeagues_LevelProgression tests level progression when streaks cross thresholds
func TestRecordTransactionForAllUserLeagues_LevelProgression(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test_user_123"

	// Create leagues where user is close to level progression
	now := time.Now()
	seasonStart := now.AddDate(0, -1, 0)
	seasonEnd := now.AddDate(0, 1, 0)

	// League where user will progress from Bronze to Silver (29 -> 30)
	leagueProgressToSilver := &league.League{
		ObjectID: primitive.NewObjectID(),
		ID:       "league_progress_silver",
		Name:     "Progress to Silver League",
		Members: []league.LeagueMember{
			{
				UserID:                 userID,
				UserName:               "Test User",
				TransactionStreak:      29, // One away from Silver
				CurrentLeagueLevel:     league.BronzeLevel,
				LastStreakActivityDate: now.AddDate(0, 0, -1),
			},
		},
		CurrentSeason: league.Season{
			StartDate: seasonStart,
			EndDate:   seasonEnd,
		},
		LevelRequirements: []league.LevelRequirement{
			{Level: league.BronzeLevel, TransactionStreakNeeded: 0},
			{Level: league.SilverLevel, TransactionStreakNeeded: 30},
			{Level: league.GoldLevel, TransactionStreakNeeded: 60},
			{Level: league.DiamondLevel, TransactionStreakNeeded: 90},
		},
	}

	// League where user will progress from Silver to Gold (59 -> 60)
	leagueProgressToGold := &league.League{
		ObjectID: primitive.NewObjectID(),
		ID:       "league_progress_gold",
		Name:     "Progress to Gold League",
		Members: []league.LeagueMember{
			{
				UserID:                 userID,
				UserName:               "Test User",
				TransactionStreak:      59, // One away from Gold
				CurrentLeagueLevel:     league.SilverLevel,
				LastStreakActivityDate: now.AddDate(0, 0, -1),
			},
		},
		CurrentSeason: league.Season{
			StartDate: seasonStart,
			EndDate:   seasonEnd,
		},
		LevelRequirements: []league.LevelRequirement{
			{Level: league.BronzeLevel, TransactionStreakNeeded: 0},
			{Level: league.SilverLevel, TransactionStreakNeeded: 30},
			{Level: league.GoldLevel, TransactionStreakNeeded: 60},
			{Level: league.DiamondLevel, TransactionStreakNeeded: 90},
		},
	}

	testLeagues := []*league.League{leagueProgressToSilver, leagueProgressToGold}

	// Create transaction date
	transactionDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)

	// Create mock league service
	mockLeagueService := &MockLeagueServiceForRecordTransaction{}
	mockLeagueService.On("FindAllLeagues", ctx, userID).Return(testLeagues, nil)
	for _, league := range testLeagues {
		mockLeagueService.On("Update", ctx, league).Return(nil)
	}

	// Act
	err := simulateRecordTransactionForAllUserLeagues(ctx, mockLeagueService, userID, transactionDate, testLeagues)

	// Assert
	assert.NoError(t, err)

	// Verify level progressions
	assert.Equal(t, 30, testLeagues[0].Members[0].TransactionStreak, "Should progress to 30 streak")
	assert.Equal(t, league.SilverLevel, testLeagues[0].Members[0].CurrentLeagueLevel, "Should progress to Silver level")

	assert.Equal(t, 60, testLeagues[1].Members[0].TransactionStreak, "Should progress to 60 streak")
	assert.Equal(t, league.GoldLevel, testLeagues[1].Members[0].CurrentLeagueLevel, "Should progress to Gold level")
}

// TestRecordTransactionForAllUserLeagues_IntegrationWithAllTransactionTypes tests the integration
// of RecordTransactionForAllUserLeagues with all three transaction types in sequence
func TestRecordTransactionForAllUserLeagues_IntegrationWithAllTransactionTypes(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test_user_123"

	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueServiceForRecordTransaction{}
	mockDreamboard := &MockDreamboardRepository{}

	service := &service{
		Repository:           mockRepo,
		LeagueService:        mockLeague,
		DreamboardRepository: mockDreamboard,
	}

	// Create test record
	record := createTestFinancialRecord(userID)
	// Set last transaction date to 3 days ago to allow new transactions
	threeDaysAgo := time.Now().AddDate(0, 0, -3)
	record.Points.LastTransactionDate = time.Date(threeDaysAgo.Year(), threeDaysAgo.Month(), threeDaysAgo.Day(), 0, 0, 0, 0, time.UTC)

	// Test 1: CreateTransaction
	transaction := &financialsheet.Transaction{
		ObjectID:      primitive.NewObjectID(),
		Category:      "food",
		MoneySource:   financialsheet.MoneySourceOpt1,
		Value:         monetary.Amount(5000),
		Date:          time.Now(),
		PaymentMethod: financialsheet.PaymentMethodOpt4,
		Type:          financialsheet.CategoryTypeCostsOfLiving,
	}

	// Mock expectations for CreateTransaction
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil).Maybe() // Called multiple times
	mockRepo.On("FindCategoryByUserAndIdentifier", ctx, userID, mock.AnythingOfType("financialsheet.CategoryIdentifier")).Return(&financialsheet.Category{
		Identifier: "food",
		Name:       "Food",
		Type:       financialsheet.CategoryTypeCostsOfLiving,
	}, nil).Maybe() // Called as needed
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, userID, mock.AnythingOfType("time.Time")).Return(nil).Maybe() // Called multiple times

	// Test 2: CreateDreamTransaction setup
	dreamID := primitive.NewObjectID().Hex()
	dreamObjID, _ := primitive.ObjectIDFromHex(dreamID)
	testDreamboard := &dreamboard.Dreamboard{
		ObjectID: primitive.NewObjectID(),
		User:     userID,
		Dreams: []*dreamboard.Dream{
			{
				ObjectID:            dreamObjID,
				Title:               "Test Dream",
				EstimatedCost:       monetary.Amount(100000),
				CurrentRaisedAmount: monetary.Amount(0),
				IsShared:            false,
			},
		},
	}
	testDream := testDreamboard.Dreams[0]

	// Mock expectations for CreateDreamTransaction
	mockRepo.On("FindByUser", ctx, userID).Return(record, nil).Maybe()
	mockDreamboard.On("FindByDreamID", ctx, dreamObjID).Return(testDreamboard, nil).Maybe()
	mockDreamboard.On("FindDream", ctx, testDreamboard.ObjectID, dreamObjID).Return(testDream, nil).Maybe()
	mockDreamboard.On("Update", ctx, mock.AnythingOfType("*dreamboard.Dreamboard")).Return(nil).Maybe()

	// Act & Assert - Test all three transaction types in sequence

	// 1. Test CreateTransaction
	_, err := service.CreateTransaction(ctx, record, transaction, false, time.UTC)
	assert.NoError(t, err, "CreateTransaction should succeed")

	// 2. Test CreateDreamTransaction
	err = service.CreateDreamTransaction(ctx, userID, dreamID, monetary.Amount(5000), time.UTC)
	assert.NoError(t, err, "CreateDreamTransaction should succeed")

	// 3. Test NoTransactions - create a fresh record for this test since NoTransactions can't be called on the same day as a regular transaction
	recordForNoTransactions := createTestFinancialRecord(userID)
	recordForNoTransactions.Points.LastTransactionDate = time.Date(threeDaysAgo.Year(), threeDaysAgo.Month(), threeDaysAgo.Day(), 0, 0, 0, 0, time.UTC)
	err = service.NoTransactions(ctx, recordForNoTransactions, time.UTC)
	assert.NoError(t, err, "NoTransactions should succeed")

	// Verify all expectations
	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
	mockDreamboard.AssertExpectations(t)

	// Verify that RecordTransactionForAllUserLeagues was called for each transaction type
	// Note: We use Maybe() in mocks so we don't assert exact call counts
}
