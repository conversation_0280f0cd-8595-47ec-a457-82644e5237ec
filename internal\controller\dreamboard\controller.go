package dreamboard

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	_dreamboard "github.com/dsoplabs/dinbora-backend/internal/service/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)
	RegisterSharedDreamRoutes(ctx context.Context, currentGroup *echo.Group)
	RegisterContributionRoutes(ctx context.Context, currentGroup *echo.Group)

	// CRUD
	Find() echo.HandlerFunc
	FindAll() echo.HandlerFunc
	FindByUser() echo.HandlerFunc
	Update() echo.HandlerFunc
	Delete() echo.HandlerFunc

	// Category CRUD
	CreateCategory() echo.HandlerFunc
	FindCategory() echo.HandlerFunc
	UpdateCategory() echo.HandlerFunc
	DeleteCategory() echo.HandlerFunc

	// Dream CRUD
	CreateDream() echo.HandlerFunc
	FindDream() echo.HandlerFunc
	FindAllDreams() echo.HandlerFunc
	FindPersonalDreams() echo.HandlerFunc
	FindSharedDreams() echo.HandlerFunc
	FindDreamDetails() echo.HandlerFunc
	UpdateDream() echo.HandlerFunc
	PatchDream() echo.HandlerFunc
	RemoveDream() echo.HandlerFunc

	// Invitation
	InviteDetails() echo.HandlerFunc
	JoinSharedDream() echo.HandlerFunc

	CreateShareLink() echo.HandlerFunc
	FindShareLink() echo.HandlerFunc
	UpdateShareLinkStatus() echo.HandlerFunc
	RegenerateShareLink() echo.HandlerFunc

	// Contributions
	FindAllContributions() echo.HandlerFunc
	FindContributionsByDreamID() echo.HandlerFunc
	UpdateContribution() echo.HandlerFunc
	UpdateContributionStatus() echo.HandlerFunc
	DeleteContribution() echo.HandlerFunc

	// Utility
	Initialize() echo.HandlerFunc
}

type controller struct {
	Service     _dreamboard.Service
	UserService user.Service
}

func New(service _dreamboard.Service, userService user.Service) Controller {
	return &controller{
		Service:     service,
		UserService: userService,
	}
}

// Routes
func (dc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	dreamboardsGroup := currentGroup.Group("/dreamboards", middlewares.AuthGuard())

	// CRUD
	dreamboardsGroup.POST("", dc.Create(), middlewares.AdminGuard())
	dreamboardsGroup.GET("/:id", dc.Find(), middlewares.AdminGuard())
	dreamboardsGroup.GET("", dc.FindAll(), middlewares.AdminGuard())
	dreamboardsGroup.GET("/me", dc.FindByUser())
	dreamboardsGroup.PUT("/:id", dc.Update(), middlewares.AdminGuard())
	dreamboardsGroup.DELETE("/:id", dc.Delete(), middlewares.AdminGuard())

	// Category CRUD
	categoriesGroup := dreamboardsGroup.Group("/:boardId/categories")
	categoriesGroup.POST("", dc.CreateCategory(), middlewares.AdminGuard())
	categoriesGroup.GET("/:id", dc.FindCategory(), middlewares.AdminGuard())
	categoriesGroup.PUT("/:id", dc.UpdateCategory(), middlewares.AdminGuard())
	categoriesGroup.DELETE("/:id", dc.DeleteCategory(), middlewares.AdminGuard())

	// Dream CRUD
	dreamsGroup := dreamboardsGroup.Group("/dreams")
	dreamsGroup.POST("", dc.CreateDream())
	dreamsGroup.GET("/:id", dc.FindDream())
	dreamsGroup.GET("", dc.FindAllDreams())
	dreamsGroup.GET("/personal", dc.FindPersonalDreams())
	dreamsGroup.GET("/shared", dc.FindSharedDreams())
	dreamsGroup.GET("/details/:dreamId", dc.FindDreamDetails())
	dreamsGroup.PUT("/:id", dc.UpdateDream())
	dreamsGroup.PATCH("/:id", dc.PatchDream())
	dreamsGroup.DELETE("/:id", dc.RemoveDream())

	// Invitation
	dreamsGroup.GET("/invites/:code", dc.InviteDetails())
	dreamsGroup.POST("/invites/join", dc.JoinSharedDream())

	// Utility
	dreamboardsGroup.POST("/initialize", dc.Initialize(), middlewares.AdminGuard())

	// Register shared dream and contribution routes
	dc.RegisterSharedDreamRoutes(ctx, currentGroup)
	dc.RegisterContributionRoutes(ctx, currentGroup)
}

// CRUD
func (dc *controller) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		_, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var board dreamboard.Dreamboard
		if err := c.Bind(&board); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid input", errors.KeyDreamboardErrorInvalidInput, err)
		}

		boardID, err := dc.Service.Create(ctx, &board)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, boardID)
	}
}

func (dc *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		boardID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		board, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		// Get optional query parameters
		completed, _ := getParam(c, "completed")
		limitStr, _ := getParam(c, "limit")
		reverseDreams, _ := getParam(c, "reverse")

		// Convert limit to an integer
		limit := 0
		if limitStr != "" {
			limit, _ = strconv.Atoi(limitStr)
		}

		// Filter dreams based on query parameters
		filteredDreams := []*dreamboard.Dream{}
		for _, dream := range board.Dreams {
			if completed != "" {
				isCompleted, _ := strconv.ParseBool(completed)
				if dream.Completed != isCompleted {
					continue
				}
			}
			filteredDreams = append(filteredDreams, dream)
		}

		// Reverse the order of dreams
		if reverseDreams == "true" {
			reverse(filteredDreams)
		}

		// Apply limit (if set and within range)
		if limit > 0 && limit < len(filteredDreams) {
			filteredDreams = filteredDreams[:limit]
		}

		board.Dreams = filteredDreams

		return c.JSON(http.StatusOK, board)
	}
}

func (dc *controller) FindAll() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		board, err := dc.Service.FindAll(ctx)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, board)
	}
}

func (dc *controller) FindByUser() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		board, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get optional query parameters
		completed, _ := getParam(c, "completed")
		limitStr, _ := getParam(c, "limit")
		reverseDreams, _ := getParam(c, "reverse")

		// Convert limit to an integer
		limit := 0
		if limitStr != "" {
			limit, _ = strconv.Atoi(limitStr)
		}

		// Filter dreams based on query parameters
		filteredDreams := []*dreamboard.Dream{}
		for _, dream := range board.Dreams {
			if completed != "" {
				isCompleted, _ := strconv.ParseBool(completed)
				if dream.Completed != isCompleted {
					continue
				}
			}
			filteredDreams = append(filteredDreams, dream)
		}

		// Reverse the order of dreams
		if reverseDreams == "true" {
			reverse(filteredDreams)
		}

		// Apply limit (if set and within range)
		if limit > 0 && limit < len(filteredDreams) {
			filteredDreams = filteredDreams[:limit]
		}

		board.Dreams = filteredDreams

		return c.JSON(http.StatusOK, board)
	}
}

func (dc *controller) Update() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		var board dreamboard.Dreamboard
		if err := c.Bind(&board); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid input", errors.KeyDreamboardErrorInvalidInput, nil)
		}
		board.ID = boardID

		if err := dc.Service.Update(ctx, &board); err != nil {
			return err
		}

		// Find the updated dreamboardboard to double check the updates
		updatedBoard, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedBoard)
	}
}

func (dc *controller) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		if err := dc.Service.Delete(ctx, boardID); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// Utility
func (dc *controller) Initialize() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var user model.User
		if err := c.Bind(&user); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid input", errors.KeyDreamboardErrorInvalidInput, err)
		}

		if err := dc.Service.Initialize(ctx, user.ID); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, nil)
	}
}

// Helper
// getParam Generic helper to get and validate parameters
func getParam(c echo.Context, key string) (string, error) {
	value := strings.TrimSpace(c.Param(key)) // Try getting from path parameters
	if value == "" {
		value = strings.TrimSpace(c.QueryParam(key)) // Try getting from query parameters
	}

	if value == "" {
		return "", errors.NewValidationError(errors.Controller, "missing or invalid param", errors.KeyDreamboardErrorMissingParam, nil)
	}

	return value, nil
}

// Helper
// reverse the order of dreams in a dreamboard
func reverse(dreams []*dreamboard.Dream) {
	for i, j := 0, len(dreams)-1; i < j; i, j = i+1, j-1 {
		dreams[i], dreams[j] = dreams[j], dreams[i]
	}
}

// Helper
// mergeDreams merges non-zero fields from patch into current dream
func mergeDreams(current, patch *dreamboard.Dream) *dreamboard.Dream {
	// Merge title if provided
	if patch.Title != "" {
		current.Title = patch.Title
	}

	// Merge category if provided and valid
	if patch.Category != dreamboard.CategoryIdentifierUndefined {
		current.Category = patch.Category
	}

	// Merge time frame if provided and valid
	if patch.TimeFrame != dreamboard.UndefinedTimeFrame {
		current.TimeFrame = patch.TimeFrame
	}

	// Merge deadline if provided and not zero
	if !patch.Deadline.IsZero() {
		current.Deadline = patch.Deadline
	}

	// Merge costs only if provided and positive (don't merge zero values as they
	// might be unspecified fields rather than explicit zeros)
	if patch.EstimatedCost > 0 {
		current.EstimatedCost = patch.EstimatedCost
	}
	if patch.MonthlySavings > 0 {
		current.MonthlySavings = patch.MonthlySavings
	}

	// Merge money sources if provided
	if len(patch.MoneySource) > 0 {
		current.MoneySource = patch.MoneySource
	}

	// Merge custom money source if provided
	if patch.CustomMoneySource != "" {
		current.CustomMoneySource = patch.CustomMoneySource
	}

	// For booleans like Completed, we override since we can't distinguish
	// between "false" and "not provided" in JSON
	current.Completed = patch.Completed

	return current
}
