package gamification

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

// Planejou o Futuro implementation
func (s *service) CheckPlanningAchievement(ctx context.Context, userID string) error {
	// Step 1: Check if user already has Explorer achievement (fail-fast)
	hasAchievement, err := s.Repository.HasAchievement(ctx, userID, planningAchievementIdentifier)
	if err != nil {
		return errors.New(errors.Service, "failed to check if user has future achievement", errors.Internal, err)
	}
	if hasAchievement {
		// User already has the achievement, no need to check further
		return nil
	}

	// Step 2: Check if user completed "plano-financeiro-phase001" challenge (fail-fast)
	hasCompletedChallenge, err := s.hasCompletedPlanoFinanceiroChallenge(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check apontamento financeiro challenge completion", errors.Internal, err)
	}
	if !hasCompletedChallenge {
		// Challenge not completed, user doesn't qualify for Future achievement
		return nil
	}

	// Step 3: Check if user has created (planned) any transactions for each one of the next 12 months (fail-fast)
	hasPlannedTransactions, err := s.hasPlannedTransactionsForNext12Months(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check planned transactions", errors.Internal, err)
	}
	if !hasPlannedTransactions {
		// User hasn't planned any transactions, doesn't qualify for Future achievement
		return nil
	}

	// Step 4: All checks passed, award the achievement
	return s.awardAchievement(ctx, userID, planningAchievementIdentifier)
}

// Helper methods to award an achievement
func (s *service) hasCompletedPlanoFinanceiroChallenge(ctx context.Context, userID string) (bool, error) {
	progressionSummary, err := s.ProgressionRepo.GetProgressSummary(ctx, userID)
	if err != nil {
		return false, err
	}

	// Look for "plano-financeiro-phase001" phase completion in any trail
	return s.hasCompletedChallengePhase(progressionSummary, planningChallengePhase001Identifier), nil
}

func (s *service) hasPlannedTransactionsForNext12Months(ctx context.Context, userID string) (bool, error) {
	now := time.Now().UTC()
	startOfPeriod := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	endOfPeriod := startOfPeriod.AddDate(0, 12, 0)

	// Fetch a unique set of "YYYY-MM" strings for months that have transactions in our range.
	monthMap, err := s.FinancialSheetRepo.FindTransactionMonthsInRange(ctx, userID, startOfPeriod, endOfPeriod)
	if err != nil {
		return false, err
	}

	for i := 0; i < 12; i++ {
		checkMonth := now.AddDate(0, i, 0)
		key := checkMonth.Format("2006-01")
		if !monthMap[key] {
			// If a month is missing from the map, we know the condition is not met.
			return false, nil
		}
	}

	return true, nil
}
