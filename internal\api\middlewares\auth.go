package middlewares

import (
	"context"
	"crypto/subtle"
	"os"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

// AuthGuard é um middleware responsável pela dupla verificação do JWT.
func AuthGuard() echo.MiddlewareFunc {
	return middleware.JWTWithConfig(middleware.JWTConfig{
		SigningKey: []byte(os.Getenv("API_ACCESS_JWT_KEY")),
	})
}

func AuthSameUserGuard(service user.Service) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			paramUserId := c.Param("id")
			userToken, err := token.GetClaimsFromRequest(c.Request())
			if err != nil {
				return err
			}

			if isAdminUser, _ := isAdmin(c.Request().Context(), service, userToken.Uid); isAdminUser {
				return next(c)
			}

			if paramUserId != userToken.Uid {
				return errors.New(errors.Middleware, "user forbidden", errors.Forbidden, nil)
			}

			return next(c)
		}
	}
}

func isAdmin(ctx context.Context, service user.Service, uid string) (bool, error) {
	user, err := service.Find(ctx, uid)
	if err != nil {
		return false, err
	}
	if user.Roles == nil || len(user.Roles) <= 0 {
		return false, errors.New(errors.Middleware, "user forbidden", errors.Forbidden, nil)
	}
	for _, role := range user.Roles {
		if role == "admin" {
			return true, nil
		}
	}

	return false, errors.New(errors.Middleware, "user forbidden", errors.Forbidden, nil)
}

// AdminGuard is deprecated, use AdminOnly() instead
// Kept for backward compatibility
func AdminGuard() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			claims, err := token.GetClaimsFromRequest(c.Request())
			if err != nil {
				return err
			}

			// Check both legacy role field and new roles array
			if claims.Role != "admin" && !claims.HasRole("admin") {
				return errors.New(errors.Middleware, "user forbidden", errors.Forbidden, nil)
			}

			return next(c)
		}
	}
}

// ServiceAuthMiddleware validates service-to-service authentication using a token
// from the Authorization header. The token should be in the format "Bearer TOKEN"
// and must match the MAIN_BACKEND_SERVICE_TOKEN environment variable.
func ServiceAuthMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get the Authorization header
			authHeader := c.Request().Header.Get("Authorization")

			// Check if the header exists and has the correct format
			if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
				return errors.New(errors.Middleware, "invalid authorization header", errors.Unauthorized, nil)
			}

			// Extract the token
			parts := strings.Split(authHeader, " ")
			if len(parts) != 2 {
				return errors.New(errors.Middleware, "invalid authorization format", errors.Unauthorized, nil)
			}

			token := parts[1]
			expectedToken := os.Getenv("MAIN_BACKEND_SERVICE_TOKEN")

			// Validate the token
			if token != expectedToken || expectedToken == "" {
				return errors.New(errors.Middleware, "invalid service token", errors.Unauthorized, nil)
			}

			// Token is valid, proceed
			return next(c)
		}
	}
}

// N8nGuard validates that a request is coming from a trusted service (like n8n)
// by checking for a secret key in the X-API-Key header.
func N8nGuard() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 1. Get the API key directly from the 'X-API-Key' header.
			providedKey := c.Request().Header.Get("X-API-Key")

			// 2. Get the expected key from environment variables.
			// It's good practice to rename this to be more specific.
			expectedKey := os.Getenv("N8N_API_KEY")

			// 3. Check if the provided key is missing or empty.
			if providedKey == "" {
				return errors.New(errors.Middleware, "API key header is missing", errors.Unauthorized, nil)
			}

			// 4. Validate the key. Use a constant-time comparison for security.
			if subtle.ConstantTimeCompare([]byte(providedKey), []byte(expectedKey)) != 1 || expectedKey == "" {
				return errors.New(errors.Middleware, "invalid API key", errors.Unauthorized, nil)
			}

			// Key is valid, proceed to the next handler.
			return next(c)
		}
	}
}

// Context keys for storing user information in Echo context
const (
	UserIDContextKey     = "user_id"
	UserRolesContextKey  = "user_roles"
	UserClaimsContextKey = "user_claims"
)

// UserContext represents the user information stored in context
type UserContext struct {
	UserID string
	Roles  []string
	Claims *token.Details
}

// UserContextMiddleware extracts user information from validated JWT and stores it in Echo context
// This middleware should be used after AuthGuard middleware
func UserContextMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Extract claims from the request (JWT should already be validated by AuthGuard)
			claims, err := token.GetClaimsFromRequest(c.Request())
			if err != nil {
				return errors.New(errors.Middleware, "failed to extract user claims", errors.Unauthorized, err)
			}

			// Create user context
			userCtx := &UserContext{
				UserID: claims.Uid,
				Roles:  claims.Roles,
				Claims: claims,
			}

			// Store user information in Echo context
			c.Set(UserIDContextKey, claims.Uid)
			c.Set(UserRolesContextKey, claims.Roles)
			c.Set(UserClaimsContextKey, userCtx)

			return next(c)
		}
	}
}

// GetUserContext extracts user context from Echo context
func GetUserContext(c echo.Context) (*UserContext, error) {
	userCtx, ok := c.Get(UserClaimsContextKey).(*UserContext)
	if !ok {
		return nil, errors.New(errors.Middleware, "user context not found", errors.Unauthorized, nil)
	}
	return userCtx, nil
}

// GetUserID extracts user ID from Echo context
func GetUserID(c echo.Context) (string, error) {
	userID, ok := c.Get(UserIDContextKey).(string)
	if !ok {
		return "", errors.New(errors.Middleware, "user ID not found in context", errors.Unauthorized, nil)
	}
	return userID, nil
}

// GetUserRoles extracts user roles from Echo context
func GetUserRoles(c echo.Context) ([]string, error) {
	roles, ok := c.Get(UserRolesContextKey).([]string)
	if !ok {
		return nil, errors.New(errors.Middleware, "user roles not found in context", errors.Unauthorized, nil)
	}
	return roles, nil
}

// RoleGuardConfig defines the configuration for role-based access control
type RoleGuardConfig struct {
	// RequiredRoles specifies the roles that are allowed to access the endpoint
	// If multiple roles are specified, the user needs ANY of them (OR logic)
	RequiredRoles []auth.Role

	// MinimumRole specifies the minimum role level required (uses role hierarchy)
	MinimumRole auth.Role

	// AllowSelf indicates whether users can access their own resources
	// When true, users can access endpoints if the resource belongs to them
	AllowSelf bool

	// SelfParamName specifies the parameter name to check for self-access
	// Default is "id" (e.g., /users/:id)
	SelfParamName string
}

// RoleGuard creates a middleware that enforces role-based access control
func RoleGuard(config RoleGuardConfig) echo.MiddlewareFunc {
	// Set default parameter name for self-access
	if config.SelfParamName == "" {
		config.SelfParamName = "id"
	}

	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get user context (should be set by UserContextMiddleware)
			userCtx, err := GetUserContext(c)
			if err != nil {
				return err
			}

			// Check if user has required roles (OR logic)
			if len(config.RequiredRoles) > 0 {
				hasRequiredRole := false
				for _, requiredRole := range config.RequiredRoles {
					if auth.HasRole(userCtx.Roles, requiredRole) {
						hasRequiredRole = true
						break
					}
				}

				if hasRequiredRole {
					return next(c)
				}
			}

			// Check minimum role level (hierarchy)
			if config.MinimumRole != "" {
				if auth.HasMinimumRole(userCtx.Roles, config.MinimumRole) {
					return next(c)
				}
			}

			// Check self-access
			if config.AllowSelf {
				paramValue := c.Param(config.SelfParamName)
				if paramValue != "" && paramValue == userCtx.UserID {
					return next(c)
				}
			}

			// Access denied
			return errors.New(errors.Middleware, "insufficient permissions", errors.Forbidden, nil)
		}
	}
}

// Convenience functions for common role guards

// AdminOnly creates a middleware that allows only admin users
// Uses MinimumRole since admin is the highest level (same effect as RequiredRoles in this case)
func AdminOnly() echo.MiddlewareFunc {
	return RoleGuard(RoleGuardConfig{
		MinimumRole: auth.RoleAdmin,
	})
}

// DirectorOrAbove creates a middleware that allows director level and above
func DirectorOrAbove() echo.MiddlewareFunc {
	return RoleGuard(RoleGuardConfig{
		MinimumRole: auth.RoleDirector,
	})
}

// TeacherOrAbove creates a middleware that allows teacher level and above
func TeacherOrAbove() echo.MiddlewareFunc {
	return RoleGuard(RoleGuardConfig{
		MinimumRole: auth.RoleTeacher,
	})
}

// SelfOrAdmin creates a middleware that allows users to access their own resources or admin users
func SelfOrAdmin() echo.MiddlewareFunc {
	return RoleGuard(RoleGuardConfig{
		RequiredRoles: []auth.Role{auth.RoleAdmin},
		AllowSelf:     true,
	})
}

// SelfOrTeacher creates a middleware that allows users to access their own resources or teacher level and above
func SelfOrTeacher() echo.MiddlewareFunc {
	return RoleGuard(RoleGuardConfig{
		MinimumRole: auth.RoleTeacher,
		AllowSelf:   true,
	})
}

// HROnly creates a middleware that allows only HR users (including company-specific HR)
// Note: This intentionally excludes Admin users - HR data should only be accessible by HR roles
func HROnly() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userCtx, err := GetUserContext(c)
			if err != nil {
				return err
			}

			if auth.IsHumanResources(userCtx.Roles) {
				return next(c)
			}

			return errors.New(errors.Middleware, "HR access required", errors.Forbidden, nil)
		}
	}
}

// HROrAdmin creates a middleware that allows HR users or admins
// Use this when admins should also have access to HR data
func HROrAdmin() echo.MiddlewareFunc {
	return RoleGuard(RoleGuardConfig{
		RequiredRoles: []auth.Role{auth.RoleAdmin, auth.RoleHumanResources},
	})
}

// ContentManagers creates a middleware for users who can manage educational content
// Example of RequiredRoles for specific business logic (not hierarchy-based)
func ContentManagers() echo.MiddlewareFunc {
	return RoleGuard(RoleGuardConfig{
		RequiredRoles: []auth.Role{auth.RoleAdmin, auth.RoleTeacher},
		// Note: Directors are excluded - they manage people, not content
	})
}
