package dreamboard

import (
	"encoding/json"
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Dream struct {
	ObjectID          primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID                string             `json:"id,omitempty" bson:"-"`
	Category          CategoryIdentifier `json:"category" bson:"category"`
	Color             string             `json:"color" bson:"color"`
	Title             string             `json:"title" bson:"title"`
	TimeFrame         TimeFrame          `json:"timeFrame" bson:"timeFrame"`
	Deadline          time.Time          `json:"deadline" bson:"deadline"`
	EstimatedCost     monetary.Amount    `json:"estimatedCost" bson:"estimatedCost"`
	MonthlySavings    monetary.Amount    `json:"monthlySavings" bson:"monthlySavings"`
	Achievable        bool               `json:"achievable" bson:"achievable"` // Check if the dream is achievable based on the deadline, estimated cost, and monthly savings
	MoneySource       []MoneySource      `json:"moneySource" bson:"moneySource"`
	Completed         bool               `json:"completed" bson:"completed"`
	CustomMoneySource string             `json:"customMoneySource,omitempty" bson:"customMoneySource,omitempty"`

	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"`

	// New fields for shared dreams
	IsShared         bool   `json:"isShared" bson:"isShared"`
	CreatorUserID    string `json:"creatorUserId" bson:"creatorUserId"`
	CreatorUserPhoto string `json:"creatorUserPhoto" bson:"creatorUserPhoto"`

	CurrentRaisedAmount      monetary.Amount `json:"currentRaisedAmount" bson:"currentRaisedAmount"`
	FundingStatus            FundingStatus   `json:"fundingStatus" bson:"fundingStatus"`
	CalculatedDurationMonths *int            `json:"calculatedDurationMonths,omitempty" bson:"calculatedDurationMonths,omitempty"`
}

func (d *Dream) Validate() error {
	if !d.Category.IsValid() {
		return errors.NewValidationError(errors.Model, "category identifier cannot be empty", errors.KeyDreamboardErrorDreamCategoryInvalid, nil)
	}
	if len(d.Title) == 0 || len(d.Title) > 100 {
		return errors.NewValidationError(errors.Model, "title must be between 1-100 characters", errors.KeyDreamboardErrorDreamTitleInvalid, nil)
	}
	if d.TimeFrame > Long {
		return errors.NewValidationError(errors.Model, "invalid time frame", errors.KeyDreamboardErrorDreamTimeFrameInvalid, nil)
	}
	if time.Until(d.Deadline) < 24*time.Hour {
		return errors.NewValidationError(errors.Model, "deadline must be at least 24 hours in the future", errors.KeyDreamboardErrorDreamDeadlineInvalid, nil)
	}
	if d.EstimatedCost < 0 {
		return errors.NewValidationError(errors.Model, "estimated cost cannot be negative", errors.KeyDreamboardErrorDreamCostNegative, nil)
	}
	if d.MonthlySavings < 0 || d.MonthlySavings > d.EstimatedCost {
		return errors.NewValidationError(errors.Model, "monthly savings must be between 0 and estimated cost", errors.KeyDreamboardErrorDreamSavingsInvalid, nil)
	}
	// if d.MoneySource == OtherMoneySource && d.CustomMoneySource == "" {
	// 	return errors.NewValidationError(errors.Model, "custom money source required when selecting 'other'", errors.KeyDreamboardErrorDreamMoneySourceInvalid, nil)
	// }
	for _, ms := range d.MoneySource {
		if ms < Salary || ms > MoneySourceOther {
			return errors.NewValidationError(errors.Model, "invalid money source in list", errors.KeyDreamboardErrorDreamMoneySourceInvalid, nil)
		}
	}

	// Validate shared dream fields
	if d.IsShared {
		if d.CreatorUserID == "" {
			return errors.NewValidationError(errors.Model, "creator user ID must be specified for shared dreams", errors.KeyDreamboardErrorDreamCreatorRequired, nil)
		}
		if d.FundingStatus != "" && !d.FundingStatus.IsValid() {
			return errors.NewValidationError(errors.Model, "invalid funding status", errors.KeyDreamboardErrorDreamFundingStatusInvalid, nil)
		}
	}

	if d.CurrentRaisedAmount < 0 {
		return errors.NewValidationError(errors.Model, "current raised amount cannot be negative", errors.KeyDreamboardErrorDreamRaisedAmountNegative, nil)
	}

	return nil
}

// IsAchievable checks if the dream is achievable in a robust and correct way,
// using integer arithmetic for monetary values.
func (d *Dream) IsAchievable() bool {
	// 1. Handle edge cases first for clarity and correctness.
	// A dream with no cost is instantly achievable.
	if d.EstimatedCost <= 0 {
		return true
	}
	// If you have a cost but are not saving anything, the dream is unachievable.
	if d.MonthlySavings <= 0 {
		return false
	}

	// 2. Calculate the number of months required, rounding UP using integer math.
	// This is a standard formula to get the ceiling of an integer division.
	// (numerator + denominator - 1) / denominator
	// Example: (1001 + 100 - 1) / 100 = 1100 / 100 = 11. Correct!
	// Example: (1000 + 100 - 1) / 100 = 1099 / 100 = 10. Correct!
	monthsNeeded := (d.EstimatedCost + d.MonthlySavings - 1) / d.MonthlySavings

	// 3. Calculate the actual date of completion using the correct number of months.
	// time.AddDate is calendar-aware and handles all date complexities.
	completionDate := time.Now().AddDate(0, int(monthsNeeded), 0)

	// 4. Compare the completion date to the deadline.
	// The dream is achievable if the completion date is *before* or *on the same day* as the deadline.
	return !completionDate.After(d.Deadline)
}

type CategoryIdentifier byte

const (
	CategoryIdentifierUndefined CategoryIdentifier = iota
	CategoryIdentifierProfessional
	CategoryIdentifierFinancial
	CategoryIdentifierLeisure
	CategoryIdentifierEmotional
	CategoryIdentifierIntellectual
	CategoryIdentifierSpiritual
	CategoryIdentifierPhysical
	CategoryIdentifierIntimate
	CategoryIdentifierSocial
	CategoryIdentifierFamilial
)

func (ci CategoryIdentifier) IsValid() bool {
	return ci >= CategoryIdentifierProfessional && ci <= CategoryIdentifierFamilial
}

func (ci CategoryIdentifier) Validate() error {
	if !ci.IsValid() {
		return errors.NewValidationError(errors.Model, "invalid category identifier value", errors.KeyDreamboardErrorDreamCategoryInvalid, nil)
	}
	return nil
}

// String implements fmt.Stringer
func (ci CategoryIdentifier) String() string {
	return ci.StringValue()
}

// StringValue returns the string representation directly
func (ci CategoryIdentifier) StringValue() string {
	switch ci {
	case CategoryIdentifierProfessional:
		return "professional"
	case CategoryIdentifierFinancial:
		return "financial"
	case CategoryIdentifierLeisure:
		return "leisure"
	case CategoryIdentifierEmotional:
		return "emotional"
	case CategoryIdentifierIntellectual:
		return "intellectual"
	case CategoryIdentifierSpiritual:
		return "spiritual"
	case CategoryIdentifierPhysical:
		return "physical"
	case CategoryIdentifierIntimate:
		return "intimate"
	case CategoryIdentifierSocial:
		return "social"
	case CategoryIdentifierFamilial:
		return "familial"
	default:
		return "undefined"
	}
}

// MarshalJSON implements json.Marshaler
func (ci CategoryIdentifier) MarshalJSON() ([]byte, error) {
	return json.Marshal(ci.StringValue())
}

// UnmarshalJSON implements json.Unmarshaler
func (ci *CategoryIdentifier) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	switch strings.ToLower(s) {
	case "professional":
		*ci = CategoryIdentifierProfessional
	case "financial":
		*ci = CategoryIdentifierFinancial
	case "leisure":
		*ci = CategoryIdentifierLeisure
	case "emotional":
		*ci = CategoryIdentifierEmotional
	case "intellectual":
		*ci = CategoryIdentifierIntellectual
	case "spiritual":
		*ci = CategoryIdentifierSpiritual
	case "physical":
		*ci = CategoryIdentifierPhysical
	case "intimate":
		*ci = CategoryIdentifierIntimate
	case "social":
		*ci = CategoryIdentifierSocial
	case "familial":
		*ci = CategoryIdentifierFamilial
	default:
		return errors.NewValidationError(errors.Model, "invalid category identifier value", errors.KeyDreamboardErrorDreamCategoryInvalid, nil)
	}
	return nil
}

type TimeFrame byte

const (
	UndefinedTimeFrame TimeFrame = iota
	Short
	Medium
	Long
)

// IsValid validates the time frame value
func (tf TimeFrame) IsValid() bool {
	return tf >= Short && tf <= Long
}

func (tf TimeFrame) Validate() error {
	if !tf.IsValid() {
		return errors.NewValidationError(errors.Model, "invalid time frame value", errors.KeyDreamboardErrorDreamTimeFrameInvalid, nil)
	}
	return nil
}

// String implements fmt.Stringer
func (tf TimeFrame) String() string {
	return tf.StringValue()
}

// StringValue returns the string representation directly
func (tf TimeFrame) StringValue() string {
	switch tf {
	case Short:
		return "short"
	case Medium:
		return "medium"
	case Long:
		return "long"
	default:
		return "undefined"
	}
}

// MarshalJSON implements json.Marshaler
func (tf TimeFrame) MarshalJSON() ([]byte, error) {
	return json.Marshal(tf.StringValue())
}

// UnmarshalJSON implements json.Unmarshaler
func (tf *TimeFrame) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	switch strings.ToLower(s) {
	case "short":
		*tf = Short
	case "medium":
		*tf = Medium
	case "long":
		*tf = Long
	default:
		return errors.NewValidationError(errors.Model, "invalid time frame value", errors.KeyDreamboardErrorDreamTimeFrameInvalid, nil)
	}
	return nil
}

type MoneySource byte

const (
	MoneySourceUndefined MoneySource = iota
	Salary
	Investments
	Freelance
	Business
	Inheritance
	Sale
	Savings
	MoneySourceOther
)

// IsValid validates the money source value
func (ms MoneySource) IsValid() bool {
	return ms >= Salary && ms <= MoneySourceOther
}

func (ms MoneySource) Validate() error {
	if !ms.IsValid() {
		return errors.NewValidationError(errors.Model, "invalid money source value", errors.KeyDreamboardErrorDreamMoneySourceInvalid, nil)
	}
	return nil
}

// String implements fmt.Stringer
func (ms MoneySource) String() string {
	return ms.StringValue()
}

// StringValue returns the string representation directly
func (ms MoneySource) StringValue() string {
	switch ms {
	case Salary:
		return "salary"
	case Investments:
		return "investments"
	case Freelance:
		return "freelance"
	case Business:
		return "business"
	case Inheritance:
		return "inheritance"
	case Sale:
		return "sale"
	case Savings:
		return "savings"
	case MoneySourceOther:
		return "other"
	default:
		return "undefined"
	}
}

// MarshalJSON implements json.Marshaler
func (ms MoneySource) MarshalJSON() ([]byte, error) {
	return json.Marshal(ms.StringValue())
}

// UnmarshalJSON implements json.Unmarshaler
func (ms *MoneySource) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	switch strings.ToLower(s) {
	case "salary":
		*ms = Salary
	case "investments":
		*ms = Investments
	case "freelance":
		*ms = Freelance
	case "business":
		*ms = Business
	case "inheritance":
		*ms = Inheritance
	case "sale":
		*ms = Sale
	case "savings":
		*ms = Savings
	case "other":
		*ms = MoneySourceOther
	default:
		return errors.NewValidationError(errors.Model, "invalid money source value", errors.KeyDreamboardErrorDreamMoneySourceInvalid, nil)
	}
	return nil
}

// FundingStatus represents the status of a dream's funding
type FundingStatus string

const (
	FundingStatusPersonalActive            FundingStatus = "PERSONAL_ACTIVE"
	FundingStatusSharedOpenForParticipants FundingStatus = "SHARED_OPEN_FOR_PARTICIPANTS"
	FundingStatusSharedActiveCollecting    FundingStatus = "SHARED_ACTIVE_COLLECTING"
	FundingStatusFullyFunded               FundingStatus = "FULLY_FUNDED"
	FundingStatusCompleted                 FundingStatus = "COMPLETED"
	FundingStatusCancelled                 FundingStatus = "CANCELLED"
)

// IsValid validates the funding status value
func (fs FundingStatus) IsValid() bool {
	switch fs {
	case FundingStatusPersonalActive, FundingStatusSharedOpenForParticipants,
		FundingStatusSharedActiveCollecting, FundingStatusFullyFunded,
		FundingStatusCompleted, FundingStatusCancelled:
		return true
	default:
		return false
	}
}
