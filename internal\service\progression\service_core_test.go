package progression

import (
	"context"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Simple test for progression validation
func TestProgressionBodyValidation(t *testing.T) {
	// Create a valid progression body
	validBody := &progression.ProgressionBody{
		Trail:   "test-trail-id",
		Module:  "lesson-1",
		Content: "lesson-1-content-1",
		Type:    "LESSON",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-1",
			Next:       "lesson-1-content-2",
		},
	}

	// Test valid progression body
	err := validBody.Validate()
	assert.NoError(t, err)

	// Test invalid type
	invalidTypeBody := &progression.ProgressionBody{
		Trail:   "test-trail-id",
		Module:  "lesson-1",
		Content: "lesson-1-content-1",
		Type:    "INVALID", // Invalid type
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-1",
			Next:       "lesson-1-content-2",
		},
	}
	err = invalidTypeBody.Validate()
	assert.Error(t, err)

	// Test missing trail
	missingTrailBody := &progression.ProgressionBody{
		Trail:   "", // Missing trail
		Module:  "lesson-1",
		Content: "lesson-1-content-1",
		Type:    "LESSON",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-1",
			Next:       "lesson-1-content-2",
		},
	}
	err = missingTrailBody.Validate()
	assert.Error(t, err)
}

// Simplified test for lesson progress tracking
func TestLessonProgress(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockCacheSvc := new(MockCacheSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockVaultSvc,
		mockCacheSvc,
		nil,
	)

	// Create test data
	ctx := context.Background()
	userId := "test-user-id"
	trailId := "test-trail-id"

	// Create mock trail
	mockTrail := &content.Trail{
		ID:         trailId,
		Name:       "Test Trail",
		Identifier: "test-trail",
		Level:      1,
		Logo:       "test-logo.png",
		Color:      "#FFFFFF",
		Lessons: []*content.Lesson{
			{
				Name:       "Lesson 1",
				Identifier: "lesson-1",
				Logo:       "lesson-1-logo.png",
				Color:      "#FF0000",
				Order:      1,
				Content: []*content.LessonContent{
					{
						Identifier:  "lesson-1-content-1",
						Description: "First content of lesson 1",
						Next:        "lesson-1-content-2",
					},
					{
						Identifier:  "lesson-1-content-2",
						Description: "Second content of lesson 1",
						Next:        "coin", // Completion marker
					},
				},
			},
		},
	}

	// Setup mocks - simplified approach
	mockTrailSvc.On("Find", mock.Anything, trailId).Return(mockTrail, nil)
	mockRepo.On("CreateEvent", mock.Anything, mock.Anything).Return(nil)
	mockRepo.On("InvalidateProgressSummary", mock.Anything, userId).Return(nil)
	mockCacheSvc.On("Delete", mock.Anything, mock.Anything).Return(nil)

	// Mock vault service calls for rewards - no rewards for non-completion
	// This test just tests the basic flow without rewards

	// Create progression body for lesson progression (not completion)
	progressionBody := &progression.ProgressionBody{
		Trail:   trailId,
		Module:  "lesson-1",
		Content: "lesson-1-content-1",
		Type:    "LESSON",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-1",
			Next:       "lesson-1-content-2", // Not a completion
		},
	}

	// Test lesson progression
	err := service.RecordProgress(ctx, userId, progressionBody)
	assert.NoError(t, err)
}

// Simplified test for lesson completion with rewards
func TestLessonCompletion(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockCacheSvc := new(MockCacheSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockVaultSvc,
		mockCacheSvc,
		nil,
	)

	// Create test data
	ctx := context.Background()
	userId := "test-user-id"
	trailId := "test-trail-id"

	// Create mock trail
	mockTrail := &content.Trail{
		ID:         trailId,
		Name:       "Test Trail",
		Identifier: "test-trail",
		Lessons: []*content.Lesson{
			{
				Name:       "Lesson 1",
				Identifier: "lesson-1",
				Content: []*content.LessonContent{
					{
						Identifier: "lesson-1-content-2",
						Next:       "coin",
					},
				},
			},
		},
	}

	// Mock progress summary for rewards calculation
	mockProgressSummary := &progression.ProgressSummary{
		UserID: userId,
		Trails: map[string]*progression.TrailSummary{
			trailId: {
				ID:          trailId,
				IsCompleted: false,
			},
		},
	}

	// Setup mocks
	mockTrailSvc.On("Find", mock.Anything, trailId).Return(mockTrail, nil)
	mockRepo.On("CreateEvent", mock.Anything, mock.Anything).Return(nil)
	mockRepo.On("InvalidateProgressSummary", mock.Anything, userId).Return(nil)
	mockCacheSvc.On("Delete", mock.Anything, mock.Anything).Return(nil)

	// Vault service mocks for rewards
	mockVaultSvc.On("Reward", mock.Anything, userId, 1).Return(nil)

	// Cache mock for GetUserProgress call in processRewards
	mockCacheSvc.On("Get", mock.Anything, mock.Anything).Return(mockProgressSummary, true)

	// Create progression body for lesson completion
	progressionBody := &progression.ProgressionBody{
		Trail:   trailId,
		Module:  "lesson-1",
		Content: "lesson-1-content-2",
		Type:    "LESSON",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-2",
			Next:       "coin", // Completion marker
		},
	}

	// Test lesson completion
	err := service.RecordProgress(ctx, userId, progressionBody)
	assert.NoError(t, err)
}

// Test for getting user progress with cache
func TestGetUserProgress(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockCacheSvc := new(MockCacheSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockVaultSvc,
		mockCacheSvc,
		nil,
	)

	ctx := context.Background()
	userId := "test-user-id"

	t.Run("Cache hit", func(t *testing.T) {
		// Reset mocks for this test
		mockCacheSvc.ExpectedCalls = nil

		mockSummary := &progression.ProgressSummary{
			UserID: userId,
		}

		// Setup cache hit
		mockCacheSvc.On("Get", mock.Anything, mock.Anything).Return(mockSummary, true)

		result, err := service.GetUserProgress(ctx, userId)

		assert.NoError(t, err)
		assert.Equal(t, userId, result.UserID)
	})

	t.Run("Cache miss - calculate from events", func(t *testing.T) {
		// Reset mocks for this test
		mockCacheSvc.ExpectedCalls = nil
		mockRepo.ExpectedCalls = nil

		// Setup cache miss
		mockCacheSvc.On("Get", mock.Anything, mock.Anything).Return(nil, false)

		// Setup repository calls
		mockRepo.On("GetProgressSummary", mock.Anything, userId).Return(nil, errors.New(errors.Service, "not found", errors.NotFound, nil))
		mockRepo.On("GetUserEvents", mock.Anything, userId, 0).Return([]*progression.ProgressEvent{}, nil)
		mockRepo.On("SaveProgressSummary", mock.Anything, mock.Anything).Return(nil)
		mockCacheSvc.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

		result, err := service.GetUserProgress(ctx, userId)

		assert.NoError(t, err)
		assert.Equal(t, userId, result.UserID)
	})
}
