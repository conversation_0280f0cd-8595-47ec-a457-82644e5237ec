package gamification

import (
	"context"
	"testing"
	"time"

	firebase "firebase.google.com/go/v4"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	_firebase "github.com/dsoplabs/dinbora-backend/internal/model/firebase"
	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Mock implementations for testing
type MockRepository struct {
	mock.Mock
}

type MockFirebaseRepo struct {
	mock.Mock
}

type MockFCMToken struct {
	FCMToken string
}

func (m *MockFirebaseRepo) FindByUserID(ctx context.Context, userID string) (*_firebase.FCMToken, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*_firebase.FCMToken), args.Error(1)
}

func (m *MockFirebaseRepo) Create(ctx context.Context, userID, token string) error {
	args := m.Called(ctx, userID, token)
	return args.Error(0)
}

func (m *MockFirebaseRepo) Delete(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockFirebaseRepo) FindByToken(ctx context.Context, token string) (*_firebase.FCMToken, error) {
	args := m.Called(ctx, token)
	return args.Get(0).(*_firebase.FCMToken), args.Error(1)
}

func (m *MockFirebaseRepo) Update(ctx context.Context, userID string, token string) error {
	args := m.Called(ctx, userID, token)
	return args.Error(0)
}

func (m *MockFirebaseRepo) FindUsersByToken(ctx context.Context, token string) ([]string, error) {
	args := m.Called(ctx, token)
	return args.Get(0).([]string), args.Error(1)
}

type MockAchievementRepo struct {
	mock.Mock
}

func (m *MockAchievementRepo) FindByIdentifier(ctx context.Context, identifier string) (*content.Achievement, error) {
	args := m.Called(ctx, identifier)
	return args.Get(0).(*content.Achievement), args.Error(1)
}

func (m *MockAchievementRepo) Create(ctx context.Context, achievement *content.Achievement) error {
	args := m.Called(ctx, achievement)
	return args.Error(0)
}

func (m *MockAchievementRepo) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockAchievementRepo) Find(ctx context.Context, id string) (*content.Achievement, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*content.Achievement), args.Error(1)
}

func (m *MockAchievementRepo) FindAll(ctx context.Context) ([]*content.Achievement, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*content.Achievement), args.Error(1)
}

func (m *MockAchievementRepo) Update(ctx context.Context, achievement *content.Achievement) error {
	args := m.Called(ctx, achievement)
	return args.Error(0)
}

func (m *MockRepository) Find(ctx context.Context, id string) (*gamification.UserAchievement, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*gamification.UserAchievement), args.Error(1)
}

func (m *MockRepository) FindByUser(ctx context.Context, userID string) (*gamification.UserAchievement, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*gamification.UserAchievement), args.Error(1)
}

func (m *MockRepository) FindUsersAchievements(ctx context.Context, userIDs []string) (map[string][]*gamification.Achievement, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).(map[string][]*gamification.Achievement), args.Error(1)
}

func (m *MockRepository) HasAchievement(ctx context.Context, userID, achievementIdentifier string) (bool, error) {
	args := m.Called(ctx, userID, achievementIdentifier)
	return args.Bool(0), args.Error(1)
}

func (m *MockRepository) CreateAchievement(ctx context.Context, userID, achievementIdentifier string) error {
	args := m.Called(ctx, userID, achievementIdentifier)
	return args.Error(0)
}

func (m *MockRepository) Update(ctx context.Context, userAchievement *gamification.UserAchievement) error {
	args := m.Called(ctx, userAchievement)
	return args.Error(0)
}

func (m *MockRepository) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockRepository) RemoveAchievement(ctx context.Context, userID, achievementIdentifier string) error {
	args := m.Called(ctx, userID, achievementIdentifier)
	return args.Error(0)
}

// Mock for progression repository
type MockProgressionRepo struct {
	mock.Mock
}

func (m *MockProgressionRepo) GetUserEvents(ctx context.Context, userID string, limit int) ([]*progression.ProgressEvent, error) {
	args := m.Called(ctx, userID, limit)
	return args.Get(0).([]*progression.ProgressEvent), args.Error(1)
}

func (m *MockProgressionRepo) GetTrailEvents(ctx context.Context, userID, trailID string) ([]*progression.ProgressEvent, error) {
	args := m.Called(ctx, userID, trailID)
	return args.Get(0).([]*progression.ProgressEvent), args.Error(1)
}

func (m *MockProgressionRepo) GetEventsByTimeRange(ctx context.Context, userID string, start, end time.Time) ([]*progression.ProgressEvent, error) {
	args := m.Called(ctx, userID, start, end)
	return args.Get(0).([]*progression.ProgressEvent), args.Error(1)
}

func (m *MockProgressionRepo) CreateEvent(ctx context.Context, event *progression.ProgressEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *MockProgressionRepo) CreateEventsBatch(ctx context.Context, events []*progression.ProgressEvent) error {
	args := m.Called(ctx, events)
	return args.Error(0)
}

func (m *MockProgressionRepo) GetProgressSummary(ctx context.Context, userID string) (*progression.ProgressSummary, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*progression.ProgressSummary), args.Error(1)
}

func (m *MockProgressionRepo) GetProgressSummaryBatch(ctx context.Context, userIds []string) ([]*progression.ProgressSummary, error) {
	args := m.Called(ctx, userIds)
	return args.Get(0).([]*progression.ProgressSummary), args.Error(1)
}

func (m *MockProgressionRepo) SaveProgressSummary(ctx context.Context, summary *progression.ProgressSummary) error {
	args := m.Called(ctx, summary)
	return args.Error(0)
}

func (m *MockProgressionRepo) InvalidateProgressSummary(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockProgressionRepo) MarkUserMigrated(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockProgressionRepo) Find(ctx context.Context, id string) (*progression.Progression, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*progression.Progression), args.Error(1)
}

func (m *MockProgressionRepo) FindByUser(ctx context.Context, userId string) (*progression.Progression, error) {
	args := m.Called(ctx, userId)
	return args.Get(0).(*progression.Progression), args.Error(1)
}

func (m *MockProgressionRepo) FindProgressionsByUsers(ctx context.Context, userIds []string) (map[string]*progression.Progression, error) {
	args := m.Called(ctx, userIds)
	return args.Get(0).(map[string]*progression.Progression), args.Error(1)
}

func (m *MockProgressionRepo) FindForCards(ctx context.Context, userId string) (map[string]*progression.Trail, error) {
	args := m.Called(ctx, userId)
	return args.Get(0).(map[string]*progression.Trail), args.Error(1)
}

func (m *MockProgressionRepo) Create(ctx context.Context, progression *progression.Progression) error {
	args := m.Called(ctx, progression)
	return args.Error(0)
}

func (m *MockProgressionRepo) Update(ctx context.Context, progression *progression.Progression) error {
	args := m.Called(ctx, progression)
	return args.Error(0)
}

func (m *MockProgressionRepo) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func TestFindUserAchievements_CheckAchievements(t *testing.T) {
	// Test the core repository functionality without CheckAchievements
	ctx := context.Background()
	userID := "test-user-id"

	mockRepo := &MockRepository{}
	mockProgressionRepo := &MockProgressionRepo{}
	service := &service{
		Repository:      mockRepo,
		ProgressionRepo: mockProgressionRepo,
	}
	mockProgressionRepo.On("GetProgressSummary", ctx, userID).Return(&progression.ProgressSummary{}, nil)
	mockRepo.On("HasAchievement", ctx, userID, "dna").Return(false, nil)
	mockRepo.On("HasAchievement", ctx, userID, "explorador").Return(false, nil)
	mockRepo.On("HasAchievement", ctx, userID, "sonhos").Return(false, nil)
	mockRepo.On("HasAchievement", ctx, userID, "apontamento-financeiro").Return(false, nil)
	mockRepo.On("HasAchievement", ctx, userID, "simulador").Return(false, nil)

	// Create a consolidated user achievement document
	userAchievementDoc := &gamification.UserAchievement{
		UserID: userID,
		Achievements: []*gamification.Achievement{
			{
				Identifier: "dna",
				EarnedAt:   time.Now(),
			},
		},
	}

	mockRepo.On("FindByUser", ctx, userID).Return(userAchievementDoc, nil)

	// Act - call repository directly
	result, err := service.FindUserAchievements(ctx, userID)

	// Assert
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, "dna", result[0].Identifier)
	mockRepo.AssertExpectations(t)
}

func TestFindUserAchievements_DirectRepositoryCall(t *testing.T) {
	// Test the repository call directly without CheckAchievements
	ctx := context.Background()
	userID := "test-user-id"

	mockRepo := &MockRepository{}

	// Create a consolidated user achievement document
	userAchievementDoc := &gamification.UserAchievement{
		UserID: userID,
		Achievements: []*gamification.Achievement{
			{
				Identifier: "dna",
				EarnedAt:   time.Now(),
			},
		},
	}

	mockRepo.On("FindByUser", ctx, userID).Return(userAchievementDoc, nil)

	// Act - call repository directly
	result, err := mockRepo.FindByUser(ctx, userID)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, userAchievementDoc, result)
	assert.Len(t, result.Achievements, 1)
	assert.Equal(t, "dna", result.Achievements[0].Identifier)
	mockRepo.AssertExpectations(t)
}

func TestCheckDNAAchievement_UserAlreadyHasAchievement(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test-user-id"

	mockRepo := &MockRepository{}

	// Create service with nil for other repos since we're only testing the early return
	service := &service{
		Repository: mockRepo,
	}

	// User already has DNA achievement
	mockRepo.On("HasAchievement", ctx, userID, "dna").Return(true, nil)

	// Act
	err := service.CheckDNAAchievement(ctx, userID)

	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
}

func TestAwardAchievement_WithNotification(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test-user-id"
	achievementIdentifier := "dna"

	mockRepo := &MockRepository{}
	mockFirebaseRepo := new(MockFirebaseRepo)
	mockAchievementRepo := new(MockAchievementRepo)
	mockFirebaseApp := new(firebase.App)
	service := &service{
		Repository:      mockRepo,
		FirebaseRepo:    mockFirebaseRepo,
		AchievementRepo: mockAchievementRepo,
		FirebaseApp:     mockFirebaseApp,
	}
	mockFirebaseRepo.On("FindByUserID", ctx, userID).Return(&_firebase.FCMToken{FCMToken: "test-token"}, nil)
	mockAchievementRepo.On("FindByIdentifier", ctx, achievementIdentifier).Return(&content.Achievement{Name: "DNA Achievement", Logo: "dna-logo.png"}, nil)

	mockRepo.On("CreateAchievement", ctx, userID, achievementIdentifier).Return(nil)

	// Mock the CreateAchievement call
	mockRepo.On("CreateAchievement", ctx, userID, achievementIdentifier).Return(nil)

	// Act
	err := service.awardAchievement(ctx, userID, achievementIdentifier)

	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
}

func TestFindUserAchievements_NoAchievements(t *testing.T) {
	// Test repository returning not found error
	ctx := context.Background()
	userID := "test-user-id"

	mockRepo := &MockRepository{}

	// Mock not found error
	notFoundErr := errors.New(errors.Repository, "user achievements not found", errors.NotFound, nil)

	mockRepo.On("FindByUser", ctx, userID).Return((*gamification.UserAchievement)(nil), notFoundErr)

	// Act - call repository directly
	result, err := mockRepo.FindByUser(ctx, userID)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	mockRepo.AssertExpectations(t)
}
