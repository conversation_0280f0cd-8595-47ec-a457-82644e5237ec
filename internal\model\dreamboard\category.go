package dreamboard

import (
	"encoding/json"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Category struct {
	ObjectID   primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID         string             `json:"id,omitempty" bson:"-"`
	Identifier string             `json:"identifier" bson:"identifier"`
	Name       string             `json:"name" bson:"name"`
	Icon       string             `json:"icon" bson:"icon"`
	Color      string             `json:"color" bson:"color"`
}

const CategoryIconCDN = "https://images.dinbora.com.br/quadro-dos-sonhos"

// Predefined categories for backward compatibility
var (
	UndefinedCategory = Category{
		Identifier: "undefined",
		Name:       "Undefined",
		Icon:       "question-mark-outline",
		Color:      "#C9B3FF",
	}
	Professional = Category{
		Identifier: "professional",
		Name:       "Profissional",
		Icon:       CategoryIconCDN + "/profissional.png",
		Color:      "#BEFFE1",
	}
	Financial = Category{
		Identifier: "financial",
		Name:       "Financeiro",
		Icon:       CategoryIconCDN + "/financeiro.png",
		Color:      "#F6E4FF",
	}
	Leisure = Category{
		Identifier: "leisure",
		Name:       "Lazer",
		Icon:       CategoryIconCDN + "/lazer.png",
		Color:      "#A4FFE6",
	}
	Emotional = Category{
		Identifier: "emotional",
		Name:       "Emocional",
		Icon:       CategoryIconCDN + "/emocional.png",
		Color:      "#E4E9FF",
	}
	Intellectual = Category{
		Identifier: "intellectual",
		Name:       "Intelectual",
		Icon:       CategoryIconCDN + "/intelectual.png",
		Color:      "#F9FFFA",
	}
	Spiritual = Category{
		Identifier: "spiritual",
		Name:       "Espiritual",
		Icon:       CategoryIconCDN + "/espiritual.png",
		Color:      "#FFD9D9",
	}
	Physical = Category{
		Identifier: "physical",
		Name:       "Físico",
		Icon:       CategoryIconCDN + "/fisico.png",
		Color:      "#D0EAFF",
	}
	Intimate = Category{
		Identifier: "intimate",
		Name:       "Amoroso",
		Icon:       CategoryIconCDN + "/amoroso.png",
		Color:      "#F6E4FF",
	}
	Social = Category{
		Identifier: "social",
		Name:       "Social",
		Icon:       CategoryIconCDN + "/social.png",
		Color:      "#BEFFE1",
	}
	Familial = Category{
		Identifier: "familial",
		Name:       "Familiar",
		Icon:       CategoryIconCDN + "/familiar.png",
		Color:      "#F0EAFF",
	}
)

// IsValid validates all fields in the category
func (c Category) IsValid() bool {
	// Check if identifier is lowercase alphanumeric with no spaces
	for _, char := range c.Identifier {
		if !((char >= 'a' && char <= 'z') || (char >= '0' && char <= '9')) {
			return false
		}
	}

	// Check if color is a valid hex code
	if !strings.HasPrefix(c.Color, "#") || len(c.Color) != 7 {
		return false
	}
	for _, char := range c.Color[1:] {
		if !((char >= '0' && char <= '9') || (char >= 'A' && char <= 'F') || (char >= 'a' && char <= 'f')) {
			return false
		}
	}

	return c.Identifier != "" && c.Name != "" && c.Icon != ""
}

func (c Category) Validate() error {
	if c.Identifier == "" {
		return errors.NewValidationError(errors.Model, "identifier cannot be empty", errors.KeyDreamboardErrorCategoryIdentifierEmpty, nil)
	}
	if c.Name == "" {
		return errors.NewValidationError(errors.Model, "name cannot be empty", errors.KeyDreamboardErrorCategoryNameEmpty, nil)
	}
	if c.Icon == "" {
		return errors.NewValidationError(errors.Model, "icon cannot be empty", errors.KeyDreamboardErrorCategoryIconEmpty, nil)
	}
	if c.Color == "" {
		return errors.NewValidationError(errors.Model, "color cannot be empty", errors.KeyDreamboardErrorCategoryColorEmpty, nil)
	}

	// Validate identifier format
	for _, char := range c.Identifier {
		if !((char >= 'a' && char <= 'z') || (char >= '0' && char <= '9')) {
			return errors.NewValidationError(errors.Model, "identifier must be lowercase alphanumeric with no spaces", errors.KeyDreamboardErrorCategoryIdentifierInvalid, nil)
		}
	}

	// Validate color format
	if !strings.HasPrefix(c.Color, "#") || len(c.Color) != 7 {
		return errors.NewValidationError(errors.Model, "color must be a valid hex code", errors.KeyDreamboardErrorCategoryColorInvalid, nil)
	}
	for _, char := range c.Color[1:] {
		if !((char >= '0' && char <= '9') || (char >= 'A' && char <= 'F') || (char >= 'a' && char <= 'f')) {
			return errors.NewValidationError(errors.Model, "color must be a valid hex code", errors.KeyDreamboardErrorCategoryColorInvalid, nil)
		}
	}

	return nil
}

// String implements fmt.Stringer and returns the identifier
func (c Category) String() string {
	return c.Identifier
}

// StringValue returns the display name
func (c Category) StringValue() string {
	return c.Name
}

// MarshalJSON implements json.Marshaler
func (c Category) MarshalJSON() ([]byte, error) {
	return json.Marshal(struct {
		Identifier string `json:"identifier"`
		Name       string `json:"name"`
		Icon       string `json:"icon"`
		Color      string `json:"color"`
	}{
		Identifier: c.Identifier,
		Name:       c.Name,
		Icon:       c.Icon,
		Color:      c.Color,
	})
}

// UnmarshalJSON implements json.Unmarshaler
func (c *Category) UnmarshalJSON(data []byte) error {
	aux := struct {
		Identifier string `json:"identifier"`
		Name       string `json:"name"`
		Icon       string `json:"icon"`
		Color      string `json:"color"`
	}{}

	if err := json.Unmarshal(data, &aux); err != nil {
		return errors.NewValidationError(errors.Model, "failed to unmarshal category", errors.KeyDreamboardErrorCategoryUnmarshalFailed, err)
	}

	c.Identifier = aux.Identifier
	c.Name = aux.Name
	c.Icon = aux.Icon
	c.Color = aux.Color

	return c.Validate()
}
