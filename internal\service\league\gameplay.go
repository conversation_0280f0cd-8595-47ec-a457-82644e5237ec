package league

import (
	"context"
	"log"
	"sort"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
)

func (s *service) StartNewSeasonInLeague(ctx context.Context, leagueID string, userID string, startDate time.Time, endDate time.Time) (*league.League, error) {
	// First check if the user has access to the league
	l, err := s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
	if err != nil {
		return nil, err
	}

	// Then check if the user is the owner (only owners can start a new season)
	if l.OwnerUserID != userID {
		return nil, errors.New(errors.Service, "user_not_league_owner_for_new_season", errors.Forbidden, nil)
	}

	if startDate.IsZero() || endDate.IsZero() || endDate.Before(startDate) {
		return nil, errors.New(errors.Service, "invalid_season_dates", errors.Validation, nil)
	}

	for i := range l.Members {
		l.Members[i].TransactionStreak = 0
		l.Members[i].LastStreakActivityDate = time.Time{}
		l.Members[i].CurrentLeagueLevel = league.BronzeLevel
	}

	l.CurrentSeason = league.Season{
		StartDate: startDate,
		EndDate:   endDate,
	}

	if err := s.Repository.Update(ctx, l); err != nil {
		return nil, err
	}
	return l, nil
}

func (s *service) RecordTransactionForAllUserLeagues(ctx context.Context, userID string, transactionDate time.Time) error {
	userLeagues, err := s.Repository.FindAllLeagues(ctx, userID)
	if err != nil {
		return err
	}

	// The incoming transactionDate is already a truncated UTC date. We can use it directly.
	// This is our standard for "today's" activity.
	todayActivityDateUTC := transactionDate

	for _, l := range userLeagues {
		memberUpdated := false
		for i, member := range l.Members {
			if member.UserID == userID {
				if member.LastStreakActivityDate.IsZero() {
					// This is the first activity for the member, handle it below.
					l.Members[i].TransactionStreak = 1
					l.Members[i].LastStreakActivityDate = todayActivityDateUTC
					l.Members[i].CurrentLeagueLevel = league.BronzeLevel
					memberUpdated = true
					break
				} else {
					// IMPORTANT: Normalize the LAST activity date to the start of its day IN UTC.
					// This ensures a reliable comparison regardless of how the time was stored or retrieved.
					lyear, lmonth, lday := member.LastStreakActivityDate.Date()
					lastActivityDateUTC := time.Date(lyear, lmonth, lday, 0, 0, 0, 0, time.UTC)

					// Now both dates are truncated UTC dates, making the comparison safe.
					// If activity is for the same day, or outside the season, skip.
					if lastActivityDateUTC.Equal(todayActivityDateUTC) ||
						todayActivityDateUTC.Before(l.CurrentSeason.StartDate) ||
						todayActivityDateUTC.After(l.CurrentSeason.EndDate) {
						continue
					}
				}

				// The logic below assumes we are proceeding with a streak update.
				l.Members[i].TransactionStreak++
				l.Members[i].LastStreakActivityDate = todayActivityDateUTC // Always store the normalized UTC date.

				sortedRequirements := make([]league.LevelRequirement, len(l.LevelRequirements))
				copy(sortedRequirements, l.LevelRequirements)
				sort.Slice(sortedRequirements, func(k, j int) bool {
					return sortedRequirements[k].TransactionStreakNeeded > sortedRequirements[j].TransactionStreakNeeded
				})

				newLevel := l.Members[i].CurrentLeagueLevel
				for _, req := range sortedRequirements {
					if l.Members[i].TransactionStreak >= req.TransactionStreakNeeded {
						newLevel = req.Level
						break
					}
				}
				l.Members[i].CurrentLeagueLevel = newLevel
				memberUpdated = true
				break
			}
		}
		if memberUpdated {
			if err := s.Repository.Update(ctx, l); err != nil {
				log.Printf("Error updating league %s for user %s transaction streak: %v", l.ID, userID, err)
			}
		}
	}
	return nil
}

func (s *service) FindLeagueRanking(ctx context.Context, leagueID string, userID string) (*league.LeagueRanking, error) {
	// First check if the user has access to the league
	l, err := s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
	if err != nil {
		return nil, err
	}

	rankings := make([]*league.UserRanking, len(l.Members))
	for i, member := range l.Members {
		rankings[i] = &league.UserRanking{
			UserID:                 member.UserID,
			UserName:               member.UserName,
			PhotoURL:               member.PhotoURL, // Refetch in the controller for the most updated information
			CurrentLeagueLevel:     member.CurrentLeagueLevel,
			TotalTransactionStreak: member.TransactionStreak,
		}
	}

	sort.Slice(rankings, func(i, j int) bool {
		if rankings[i].TotalTransactionStreak != rankings[j].TotalTransactionStreak {
			return rankings[i].TotalTransactionStreak > rankings[j].TotalTransactionStreak
		}
		return rankings[i].UserName < rankings[j].UserName
	})

	leagueRanking := &league.LeagueRanking{}
	if len(rankings) > 3 {
		leagueRanking.Podium = rankings[:3]
		generalLimit := 20
		if len(rankings[3:]) < generalLimit {
			generalLimit = len(rankings[3:])
		}
		leagueRanking.General = rankings[3 : 3+generalLimit]
	} else {
		leagueRanking.Podium = rankings
		leagueRanking.General = []*league.UserRanking{}
	}

	return leagueRanking, nil
}
