package dreamboard

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/labstack/echo/v4"
)

// RegisterSharedDreamRoutes registers shared dream related routes
func (dc *controller) RegisterSharedDreamRoutes(ctx context.Context, currentGroup *echo.Group) {
	// Shared Dreams routes
	// sharedDreamsGroup := currentGroup.Group("/dreamboards/dreams/shared", middlewares.AuthGuard())

	// // Invite Management
	// sharedDreamsGroup.GET("/invite/:token", dc.GetInviteDetails())
	// sharedDreamsGroup.POST("/join", dc.JoinSharedDream())

	// // Dream Dashboard
	// sharedDreamsGroup.GET("/:dreamId/dashboard", dc.GetDreamDashboard())

	// // Share Link Management
	// sharedDreamsGroup.POST("/:dreamId/share-link", dc.CreateShareLink())
	// sharedDreamsGroup.GET("/:dreamId/share-link", dc.GetShareLink())
	// sharedDreamsGroup.PUT("/:dreamId/share-link/status", dc.UpdateShareLinkStatus())
	// sharedDreamsGroup.POST("/:dreamId/share-link/regenerate", dc.RegenerateShareLink())
}

// InviteDetails retrieves invitation details for a share link token
func (dc *controller) InviteDetails() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		token := c.Param("code")

		if token == "" {
			return errors.NewWithTranslationKey(errors.Controller, "code is required", errors.KeyDreamboardErrorCodeRequired, errors.BadRequest, nil)
		}

		inviteDetails, err := dc.Service.InviteDetails(ctx, token)
		if err != nil {
			return err
		}

		// Get creator user details
		user, err := dc.UserService.Find(ctx, inviteDetails.CreatorUserID)
		if err != nil {
			return err
		}

		inviteDetails.CreatorUserName = user.Name
		inviteDetails.CreatorPhotoURL = user.PhotoURL

		return c.JSON(http.StatusOK, inviteDetails)
	}
}

// JoinSharedDream allows a user to join a shared dream
func (dc *controller) JoinSharedDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var joinSharedDreamRequest JoinSharedDreamRequest

		if err := c.Bind(&joinSharedDreamRequest); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid input", errors.KeyDreamboardErrorInvalidInput, err)
		}
		if err := c.Validate(&joinSharedDreamRequest); err != nil {
			return errors.NewValidationError(errors.Controller, "code and monthly pledged amount are required", errors.KeyDreamboardErrorValidationRequired, err)
		}

		contribution, err := dc.Service.JoinSharedDream(ctx, joinSharedDreamRequest.Code, userToken.Uid, false, joinSharedDreamRequest.MonthlyPledgedAmount)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, contribution)
	}
}
