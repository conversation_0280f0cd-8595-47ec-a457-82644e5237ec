package dreamboard

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ShareLink represents the mechanism for inviting users to a shared dream
type ShareLink struct {
	ObjectID  primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID        string             `json:"id,omitempty" bson:"-"`
	DreamID   string             `json:"dreamId" bson:"dreamId"`
	Code      string             `json:"code" bson:"code"`
	IsEnabled bool               `json:"isEnabled" bson:"isEnabled"`
	ExpiresAt time.Time          `json:"expiresAt" bson:"expiresAt"`
	CreatedAt time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// Validate validates the ShareLink
func (sl *ShareLink) Validate() error {
	if sl.DreamID == "" {
		return errors.NewValidationError(errors.Model, "dream ID must be specified", errors.KeyDreamboardErrorShareLinkDreamIdRequired, nil)
	}
	if sl.Code == "" {
		return errors.NewValidationError(errors.Model, "code must be specified", errors.KeyDreamboardErrorShareLinkCodeRequired, nil)
	}
	if sl.ExpiresAt.Before(time.Now()) {
		return errors.NewValidationError(errors.Model, "expiration date must be in the future", errors.KeyDreamboardErrorShareLinkExpired, nil)
	}
	return nil
}
