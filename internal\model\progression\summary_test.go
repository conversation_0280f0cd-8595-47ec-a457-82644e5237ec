package progression

import (
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ProgressSummaryTestSuite struct {
	suite.Suite
}

func (s *ProgressSummaryTestSuite) TestProgressSummary_Valid() {
	validID := primitive.NewObjectID().Hex()
	summary := &ProgressSummary{
		UserID: validID,
		Trails: map[string]*TrailSummary{
			validID: {
				ID:                 validID,
				LessonsCompleted:   map[string]bool{"lesson1": true},
				ChallengeCompleted: true,
				CurrentItem:        "item1",
				ProgressPercent:    100,
				IsCompleted:        true,
				TotalRewards:       10,
				LessonsRewarded:    map[string]bool{"lesson1": true},
				ChallengeRewarded:  true,
				LessonProgress: map[string]*LessonProgress{
					"lesson1": {
						Current:   "content1",
						Completed: true,
						Rewarded:  true,
						Path:      []string{"content1"},
					},
				},
				ChallengeProgress: &ChallengeProgress{
					Current:   "phase1",
					Completed: true,
					Rewarded:  true,
					Phases: map[string]*PhaseProgress{
						"phase1": {
							Current:   "content1",
							Completed: true,
							Path:      []string{"content1"},
						},
					},
				},
			},
		},
		UpdatedAt: time.Now(),
		Revision:  1,
	}
	s.True(summary.IsValid(), "Expected valid ProgressSummary to be valid")
}

func (s *ProgressSummaryTestSuite) TestProgressSummary_InvalidUserID() {
	invalidID := "notanobjectid"
	summary := &ProgressSummary{
		UserID:    invalidID,
		Trails:    map[string]*TrailSummary{},
		UpdatedAt: time.Now(),
		Revision:  1,
	}
	s.False(summary.IsValid(), "Expected ProgressSummary with invalid UserID to be invalid")
}

func (s *ProgressSummaryTestSuite) TestTrailSummary_InvalidID() {
	invalidID := "notanobjectid"
	ts := &TrailSummary{
		ID:                 invalidID,
		LessonsCompleted:   map[string]bool{"lesson1": true},
		ChallengeCompleted: true,
		CurrentItem:        "item1",
		ProgressPercent:    100,
		IsCompleted:        true,
		TotalRewards:       10,
		LessonsRewarded:    map[string]bool{"lesson1": true},
		ChallengeRewarded:  true,
		LessonProgress:     map[string]*LessonProgress{},
		ChallengeProgress:  &ChallengeProgress{},
	}
	s.False(ts.IsValid(), "Expected TrailSummary with invalid ID to be invalid")
}

func (s *ProgressSummaryTestSuite) TestTrailSummary_ValidWithNonObjectIDLessonAndChallengeIDs() {
	validID := primitive.NewObjectID().Hex()
	ts := &TrailSummary{
		ID:                 validID,
		LessonsCompleted:   map[string]bool{"lesson1": true},
		ChallengeCompleted: true,
		CurrentItem:        "item1",
		ProgressPercent:    100,
		IsCompleted:        true,
		TotalRewards:       10,
		LessonsRewarded:    map[string]bool{"lesson1": true},
		ChallengeRewarded:  true,
		LessonProgress:     map[string]*LessonProgress{"lesson1": {Current: "content1", Completed: true, Rewarded: true, Path: []string{"content1"}}},
		ChallengeProgress:  &ChallengeProgress{Current: "phase1", Completed: true, Rewarded: true, Phases: map[string]*PhaseProgress{"phase1": {Current: "content1", Completed: true, Path: []string{"content1"}}}},
	}
	s.True(ts.IsValid(), "Expected TrailSummary with non-ObjectID lesson/challenge IDs to be valid")
}

func TestProgressSummaryTestSuite(t *testing.T) {
	suite.Run(t, new(ProgressSummaryTestSuite))
}
