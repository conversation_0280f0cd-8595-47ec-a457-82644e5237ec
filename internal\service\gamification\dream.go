package gamification

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
)

// Semando Sonhos implementation
func (s *service) CheckDreamAchievement(ctx context.Context, userID string) error {
	// Step 1: Check if user already has Explorer achievement (fail-fast)
	hasAchievement, err := s.Repository.HasAchievement(ctx, userID, dreamAchievementIdentifier)
	if err != nil {
		return errors.New(errors.Service, "failed to check if user has dream achievement", errors.Internal, err)
	}
	if hasAchievement {
		// User already has the achievement, no need to check further
		return nil
	}

	// Step 2: Check if user completed "importancia-dos-sonhos-phase001" challenge (fail-fast)
	hasCompletedChallenge, err := s.hasCompletedImportanciaDosSonhosChallenge(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check importancia dos sonhos challenge completion", errors.Internal, err)
	}
	if !hasCompletedChallenge {
		// Challenge not completed, user doesn't qualify for Dream achievement
		return nil
	}

	// Step 3: Check if user has at least one dream (personal or shared) registered on each category from the dreamboard (fail-fast)
	hasRegisteredDreams, err := s.hasRegisteredDreamsOnEachCategory(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check dream registration", errors.Internal, err)
	}
	if !hasRegisteredDreams {
		// User hasn't registered dreams on each category, doesn't qualify for Dream achievement
		return nil
	}

	// Step 4: Check if the user has created at least three shared dreams (fail-fast)
	hasCreatedSharedDreams, err := s.hasCreatedSharedDreams(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check shared dreams creation", errors.Internal, err)
	}
	if !hasCreatedSharedDreams {
		// User hasn't created three shared dreams, doesn't qualify for Dream achievement
		return nil
	}

	// Step 5: All checks passed, award the achievement
	return s.awardAchievement(ctx, userID, dreamAchievementIdentifier)
}

// Helper methods to award an achievement
func (s *service) hasCompletedImportanciaDosSonhosChallenge(ctx context.Context, userID string) (bool, error) {
	progressionSummary, err := s.ProgressionRepo.GetProgressSummary(ctx, userID)
	if err != nil {
		return false, err
	}

	// Look for "importancia-dos-sonhos-phase001" phase completion in any trail
	return s.hasCompletedChallengePhase(progressionSummary, dreamChallengePhase001Identifier), nil
}

func (s *service) hasRegisteredDreamsOnEachCategory(ctx context.Context, userID string) (bool, error) {
	// Get the userDreamboard
	userDreamboard, err := s.DreamboardRepo.FindByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	// Check if there is at least one dream on each category
	categoryMap := make(map[dreamboard.CategoryIdentifier]bool)
	for _, dream := range userDreamboard.Dreams {
		categoryMap[dream.Category] = true
	}

	return len(categoryMap) == len(userDreamboard.Categories), nil
}

func (s *service) hasCreatedSharedDreams(ctx context.Context, userID string) (bool, error) {
	// Get the userDreamboard
	userDreamboard, err := s.DreamboardRepo.FindByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	// Check if there are at least three shared dreams
	sharedDreamCount := 0
	for _, dream := range userDreamboard.Dreams {
		if dream.IsShared {
			sharedDreamCount++
		}
		if sharedDreamCount >= 3 {
			return true, nil
		}
	}

	return false, nil
}
