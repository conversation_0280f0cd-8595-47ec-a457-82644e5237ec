package financialsheet

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MockLeagueService for testing - only implements the method we need
type MockLeagueService struct {
	mock.Mock
}

func (m *MockLeagueService) RecordTransactionForAllUserLeagues(ctx context.Context, userID string, transactionDate time.Time) error {
	args := m.Called(ctx, userID, transactionDate)
	return args.Error(0)
}

// Implement other required methods as no-ops for testing
func (m *MockLeagueService) CreateLeague(ctx context.Context, ownerUserID string, ownerUserName string, ownerPhotoURL string, leagueName string, startDate time.Time, endDate time.Time, timezone *time.Location) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) FindLeague(ctx context.Context, leagueID string, userID string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) FindAllLeagues(ctx context.Context, userID string) ([]*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) PatchLeague(ctx context.Context, leagueID string, userID string, name *string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) DeleteLeague(ctx context.Context, leagueID string, userID string) error {
	return nil
}

func (m *MockLeagueService) InviteDetails(ctx context.Context, code string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) JoinLeague(ctx context.Context, userID string, userName string, photoURL string, inviteCode string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) LeaveLeague(ctx context.Context, leagueID string, userID string) error {
	return nil
}

func (m *MockLeagueService) StartNewSeasonInLeague(ctx context.Context, leagueID string, userID string, startDate time.Time, endDate time.Time) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) FindLeagueRanking(ctx context.Context, leagueID string, userID string) (*league.LeagueRanking, error) {
	return nil, nil
}

func (m *MockLeagueService) FindLeagueCard(ctx context.Context, leagueID string, userID string, userTimezone *time.Location) (*league.LeagueCard, error) {
	return nil, nil
}

func (m *MockLeagueService) FindAllLeaguesCards(ctx context.Context, userID string, userTimezone *time.Location) ([]*league.LeagueCard, error) {
	return nil, nil
}

// MockGamificationService for testing - only implements the method we need
type MockGamificationService struct {
	mock.Mock
}

func (m *MockGamificationService) CheckExplorerAchievement(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationService) CheckPlanningAchievement(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationService) CheckDNAAchievement(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationService) CheckDreamAchievement(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationService) CheckInvestingAchievement(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockGamificationService) AwardAchievement(ctx context.Context, userID string, achievementIdentifier string) error {
	args := m.Called(ctx, userID, achievementIdentifier)
	return args.Error(0)
}

func (m *MockGamificationService) DetermineVisibleHeroesForUser(ctx context.Context, userID string) ([]*gamification.Hero, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*gamification.Hero), args.Error(1)
}

func (m *MockGamificationService) FindUserAchievements(ctx context.Context, userID string) ([]*gamification.Achievement, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*gamification.Achievement), args.Error(1)
}

func (m *MockGamificationService) FindContentAchievements(ctx context.Context) ([]*content.Achievement, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*content.Achievement), args.Error(1)
}

func (m *MockGamificationService) FindUsersAchievements(ctx context.Context, userIDs []string) (map[string][]*gamification.Achievement, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).(map[string][]*gamification.Achievement), args.Error(1)
}

func (m *MockGamificationService) CheckAchievements(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func TestNoTransactions_Success_FirstTime(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             0,
			Best:                0,
			LastTransactionDate: time.Time{}, // Zero time - first time
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	err := service.NoTransactions(ctx, record, time.UTC)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 1, record.Points.Current)
	assert.Equal(t, 1, record.Points.Best)
	assert.False(t, record.Points.LastTransactionDate.IsZero())
	assert.False(t, record.Points.LastNoTransactionDate.IsZero())
	assert.Len(t, record.Points.NoTransactionDates, 1)

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestNoTransactions_Success_ContinueStreak(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	yesterday := time.Now().AddDate(0, 0, -1)
	yesterdayNormalized := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.UTC)

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             5,
			Best:                10,
			LastTransactionDate: yesterdayNormalized,
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	err := service.NoTransactions(ctx, record, yesterday.Location())

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 6, record.Points.Current) // Streak should continue
	assert.Equal(t, 10, record.Points.Best)   // Best should remain the same

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestNoTransactions_Success_NewBestStreak(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	yesterday := time.Now().AddDate(0, 0, -1)
	yesterdayNormalized := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.UTC)

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             10,
			Best:                10,
			LastTransactionDate: yesterdayNormalized,
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	err := service.NoTransactions(ctx, record, yesterday.Location())

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 11, record.Points.Current) // Streak should continue
	assert.Equal(t, 11, record.Points.Best)    // Best should be updated

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestNoTransactions_Success_ResetStreak(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	threeDaysAgo := time.Now().AddDate(0, 0, -3)
	threeDaysAgoNormalized := time.Date(threeDaysAgo.Year(), threeDaysAgo.Month(), threeDaysAgo.Day(), 0, 0, 0, 0, threeDaysAgo.Location())

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             5,
			Best:                10,
			LastTransactionDate: threeDaysAgoNormalized,
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	err := service.NoTransactions(ctx, record, threeDaysAgo.Location())

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 1, record.Points.Current)  // Streak should reset
	assert.Equal(t, 10, record.Points.Best)    // Best should remain the same
	assert.Len(t, record.Points.MissedDays, 1) // Should have one missed day

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestNoTransactions_Error_NilRecord(t *testing.T) {
	// Arrange
	ctx := context.Background()
	service := &service{}

	// Act
	err := service.NoTransactions(ctx, nil, time.UTC)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid record")
}

// The old test is now incorrect.
// func TestNoTransactions_Error_AlreadyMarkedToday(t *testing.T) { ... }

// This is the new, correct test.
func TestNoTransactions_Idempotency_WhenAlreadyMarkedToday(t *testing.T) {
	// Arrange
	ctx := context.Background()

	// 1. Set up mocks for downstream services
	mockRepo := &MockRepository{} // Assuming you have this from other tests
	mockLeagueService := &MockLeagueService{}
	mockGamificationService := &MockGamificationService{}

	// 2. Create the service with the mocked dependencies
	service := &service{
		Repository:          mockRepo, // The repo should NOT be called
		LeagueService:       mockLeagueService,
		GamificationService: mockGamificationService,
	}

	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, time.UTC)

	// 3. Create the initial state of the record
	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:               5,
			Best:                  10,
			LastTransactionDate:   todayNormalized,
			LastNoTransactionDate: todayNormalized, // Key setup: Already marked today
		},
		// ... other fields
	}

	// 4. IMPORTANT: Make a copy of the points struct before the call
	// This allows us to assert that it was NOT modified.
	initialPointsState := record.Points

	// 5. Set up mock expectations
	// We expect the downstream services to be called, even on a duplicate request.
	mockLeagueService.On("RecordTransactionForAllUserLeagues", ctx, record.UserID, todayNormalized).Return(nil).Once()
	mockGamificationService.On("CheckExplorerAchievement", ctx, record.UserID).Return(nil).Once()
	// We do NOT expect the repository's Update method to be called.
	// mockRepo.AssertNotCalled(t, "Update") // You can add this if your mock framework supports it.

	// Act
	err := service.NoTransactions(ctx, record, today.Location())

	// Assert
	// 1. The function should return no error.
	assert.NoError(t, err)

	// 2. The user's personal record points should be unchanged.
	assert.Equal(t, initialPointsState, record.Points, "The user's points/streak data should not have been modified")

	// 3. Verify that the downstream services were still called.
	mockLeagueService.AssertExpectations(t)
	mockGamificationService.AssertExpectations(t)
}

func TestNoTransactions_Error_RepositoryFailure(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             0,
			Best:                0,
			LastTransactionDate: time.Time{},
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations - repository fails
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(errors.New(errors.Repository, "database error", errors.Internal, nil))

	// Act
	err := service.NoTransactions(ctx, record, time.UTC)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database error")

	mockRepo.AssertExpectations(t)
}

func TestNoTransactions_Success_LeagueServiceFailure(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             0,
			Best:                0,
			LastTransactionDate: time.Time{},
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations - league service fails but should not affect the operation
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(errors.New(errors.Service, "league error", errors.Internal, nil))

	// Act
	err := service.NoTransactions(ctx, record, time.UTC)

	// Assert
	assert.NoError(t, err) // Should succeed despite league service failure
	assert.Equal(t, 1, record.Points.Current)
	assert.Equal(t, 1, record.Points.Best)

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestNoTransactions_RealWorldScenario_TransactionYesterdayNoTransactionToday(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	// Simulate: User had a transaction yesterday and already has a streak of 1
	yesterday := time.Now().AddDate(0, 0, -1)
	yesterdayNormalized := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.UTC)

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             1,                   // Already has 1 day streak from yesterday's transaction
			Best:                1,                   // Best is also 1
			LastTransactionDate: yesterdayNormalized, // Last transaction was yesterday
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act - Call NoTransactions today
	err := service.NoTransactions(ctx, record, yesterday.Location())

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 2, record.Points.Current, "Streak should continue from 1 to 2") // This should be 2, not 1
	assert.Equal(t, 2, record.Points.Best, "Best should be updated to 2")
	assert.False(t, record.Points.LastTransactionDate.IsZero())
	assert.False(t, record.Points.LastNoTransactionDate.IsZero())
	assert.Len(t, record.Points.NoTransactionDates, 1)

	// Verify the date is today
	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, time.UTC)
	assert.True(t, record.Points.LastTransactionDate.Equal(todayNormalized), "LastTransactionDate should be today")

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

// RENAME THE TEST to reflect its new purpose.
func TestNoTransactions_Idempotency_WhenRegularTransactionExists(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeagueService := &MockLeagueService{}
	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeagueService,
		// Add mockGamificationService if needed
	}

	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, time.UTC)

	record := &financialsheet.Record{
		UserID: "user123",
		Points: financialsheet.Points{
			Current:             1,
			Best:                1,
			LastTransactionDate: todayNormalized, // Key setup: Already had a transaction today
		},
		// ... other fields
	}

	// Take a snapshot of the state before the call
	initialPointsState := record.Points

	// Set mock expectations: The LeagueService SHOULD be called. The Repo SHOULD NOT.
	mockLeagueService.On("RecordTransactionForAllUserLeagues", ctx, record.UserID, todayNormalized).Return(nil).Once()
	// mockRepo.AssertNotCalled(t, "Update") // If your mock framework supports it

	// Act - Try to call NoTransactions on the same day as a regular transaction
	err := service.NoTransactions(ctx, record, today.Location())

	// Assert
	// 1. The function should succeed without error.
	assert.NoError(t, err)

	// 2. The user's points data should be completely unchanged.
	assert.Equal(t, initialPointsState, record.Points, "Points data should not be modified when a regular transaction already exists")

	// 3. The downstream service should have been notified.
	mockLeagueService.AssertExpectations(t)
}
