# Build stage
FROM golang:1.24.5 AS builder

WORKDIR /app

# Copy only go.mod and go.sum files for better caching
COPY go.mod go.sum ./
RUN go mod download

# Copy the rest of the source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -v -o /app/dinbora ./cmd/dinbora/

# Final stage
FROM alpine:latest

WORKDIR /app

# Copy the compiled binary
COPY --from=builder /app/dinbora .

# Copy the .env file to the final image
COPY .env .env

# Copy the Health Check file to the final image
COPY health-check.sh /health-check.sh
RUN chmod +x /health-check.sh

EXPOSE 8080

CMD ["/app/dinbora"]