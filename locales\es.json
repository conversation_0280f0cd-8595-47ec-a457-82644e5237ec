{"apple": {"error": {"accessTokenRequired": "¡Uy! Tu sesión ha expirado. Por favor, inicia sesión de nuevo.", "invalidOboardingAgeRange": "Mmm, esa edad parece un poco extraña. ¿Qué tal si lo intentas de nuevo?", "invalidOboardingFinancialSituation": "Parece que esa opción no es válida. Por favor, elige una de la lista.", "invalidOboardingFinancialGoal": "Parece que esa meta no es una opción válida. Por favor, elige una de la lista.", "invalidOboardingPersonalInterest": "Parece que ese interés no es una opción válida. Por favor, elige uno de la lista.", "failedToProcessPhoto": "¡Uy! Algo salió mal con tu foto. ¿Probamos con otra?", "invalidFileType": "Mmm, este tipo de archivo no funciona. Por favor, prueba con una foto en formato JPG, JPEG, PNG o HEIC.", "fileTooLarge": "¡Esa foto es un poco pesada! Intenta con una imagen más pequeña (hasta 5MB).", "invalidLoginInput": "¡Uy! Parece que falta un campo. Echa un vistazo a los datos e inténtalo de nuevo.", "": "", "fetchFailed": "¡Ups! No pudimos obtener tu información de Apple. Inténtalo de nuevo.", "decodeFailed": "¡Ups! Tuvimos un problemita para procesar tu información de Apple. Inténtalo de nuevo.", "invalidToken": "¡Ups! Tu token de acceso ha expirado. Por favor, inicia sesión de nuevo.", "missingKid": "¡Ups! No pudimos encontrar la clave de seguridad. Inténtalo de nuevo.", "publicKeyParseFailed": "¡Ups! Tuvimos un problemita para procesar la clave de seguridad. Inténtalo de nuevo."}}, "auth": {"error": {"invalidLoginInput": "¡Uy! Parece que un campo está vacío. Revisa tus datos e inténtalo de nuevo.", "loginValidationFailed": "Mmm, ese correo o contraseña no parecen correctos. ¿Quieres intentarlo una vez más?", "invalidRegisterInput": "¡Uy! Parece que un campo está vacío. Por favor, rellena todos los datos para continuar.", "failedToParseFormData": "¡Oh, oh! Tuvimos un problemita para leer tu información. ¿Podrías intentar enviarla de nuevo?", "invalidOboardingAgeRange": "Mmm, parece que algo no está bien con el rango de edad. Por favor, revísalo e inténtalo de nuevo.", "invalidOboardingFinancialSituation": "¡Uy, algo en tu situación financiera parece incorrecto. ¿Podrías revisarlo?", "invalidOboardingFinancialGoal": "Mmm, no entendimos bien tu meta financiera. ¿Qué tal si la revisas y lo intentas de nuevo?", "invalidOboardingPersonalInterest": "Parece que tus intereses no se guardaron. ¡Intentémoslo una vez más!", "failedToProcessPhoto": "No pudimos subir tu foto. ¿Quizás intentar con otra imagen o volver a probar en un momento?", "invalidFileType": "¡Ese tipo de archivo no es compatible! Por favor, usa una imagen JPG, JPEG, PNG o HEIC.", "fileTooLarge": "¡<PERSON><PERSON>, esa foto es muy grande! Por favor, intenta con una de 5MB o menos.", "invalidRefreshTokenInput": "¡Uy, algo inesperado ocurrió de nuestro lado! Por favor, inténtalo de nuevo.", "invalidForgotPasswordInput": "<PERSON><PERSON>, algo salió mal. Revisa tus datos e intenta solicitar la recuperación de nuevo.", "invalidResetPasswordInput": "¡Uy! Algo salió mal al restablecer tu contraseña. Vamos a intentarlo una vez más.", "invalidCheckPasswordInput": "Esa contraseña no parece correcta. Échale un vistazo e inténtalo de nuevo.", "userNotLoggedIn": "Parece que no has iniciado sesión. ¡Inicia sesión para continuar tu aventura!", "invalidAdminLoginInput": "Mmm, los datos de acceso del administrador no parecen correctos. Revísalos e inténtalo de nuevo.", "": "", "failedToUploadPhoto": "¡Ups! No pudimos subir tu foto. ¿Qué tal intentar con otra imagen?", "failedToCreateToken": "¡Ups! Algo salió mal de nuestro lado. Por favor, inténtalo de nuevo en un momento.", "failedToRetrieveUserAfterCreation": "Tu cuenta fue creada, pero no pudimos conectarte ahora. ¿Qué tal intentar iniciar sesión?", "brevoNotifierNotAvailable": "¡Ups! No pudimos enviar el email ahora. ¿Qué tal intentar de nuevo en unos minutos?", "userNotAdmin": "¡Ups! Parece que esta es una área restringida. ¡Sólo para los maestros del juego!", "userNotHR": "¡Ups! Parece que esta es una área restringida. ¡Sólo para los RH!", "invalidHRLoginInput": "Mmm, los datos de acceso del departamento de recursos humanos no parecen correctos. Revísalos e inténtalo de nuevo."}}, "financialsheet": {"error": {"conflict": "¡Ya tienes una hoja financiera! Sigue organizando tus finanzas.", "createFailed": "¡Ups! No pudimos crear tu hoja financiera. Inténtalo de nuevo.", "findFailed": "¡Ups! No pudimos cargar tu hoja financiera. Inténtalo de nuevo.", "findAllFailed": "¡Ups! No pudimos cargar las hojas financieras. Inténtalo de nuevo.", "notFound": "Hoja financiera no encontrada. ¿Qué tal crear una nueva?", "invalidId": "ID de hoja financiera inválido. Verifica los datos e inténtalo de nuevo.", "userNotFound": "Hoja financiera no encontrada para este usuario. ¡Comienza creando una!", "findByUserFailed": "¡Ups! No pudimos cargar tu hoja financiera. Inténtalo de nuevo.", "findByUsersFailed": "¡Ups! No pudimos cargar las hojas financieras. Inténtalo de nuevo.", "conflictUpdate": "No se pudo actualizar tu hoja financiera. Inténtalo de nuevo.", "updateFailed": "¡Ups! No pudimos guardar tus cambios. Inténtalo de nuevo.", "deleteFailed": "¡Ups! No pudimos eliminar tu hoja financiera. Inténtalo de nuevo.", "invalidMonth": "¡Ups! El mes debe estar entre 1 y 12. Verifica e inténtalo de nuevo.", "duplicateMonth": "¡Vaya! Ya elegiste este mes. Selecciona meses diferentes para la recurrencia.", "sameMonthAsOriginal": "No puedes repetir en el mismo mes de la transacción original. ¡Elige otros meses!", "recordAlreadyExists": "¡Ya tienes una hoja financiera! Sigue organizando tus finanzas.", "invalidRecord": "¡Ups! Datos inválidos. Verifica tu información e inténtalo de nuevo.", "noTransactionsAlreadyMarked": "¡Ya marcaste 'sin transacciones' para hoy! Continúa tu racha mañana.", "noTransactionsAlreadyMarkedDate": "Ya marcaste 'sin transacciones' para esta fecha. ¡Elige otro día!", "cannotMarkSameDayAsTransaction": "No puedes marcar 'sin transacciones' el mismo día de una transacción real.", "invalidFinancialSheetId": "ID de hoja financiera inválido. Verifica los datos e inténtalo de nuevo.", "invalidMonthParameter": "¡Mes inválido! Debe estar entre 1 y 12. Verifica e inténtalo de nuevo.", "invalidYearParameter": "¡Año inválido! Verifica el formato e inténtalo de nuevo.", "invalidInput": "¡Ups! Datos inválidos. Verifica la información e inténtalo de nuevo.", "validationFailed": "Algunos campos no están completados correctamente. ¡Échales un vistazo!", "dreamTransactionInput": "¡Ups! No pudimos procesar los datos de tu sueño. Inténtalo de nuevo.", "dreamTransactionValidation": "Algunos datos de tu sueño son incorrectos. Verifica e inténtalo de nuevo.", "invalidTransactionId": "ID de transacción inválido. Verifica los datos e inténtalo de nuevo.", "invalidCategoryIdParam": "¡Ups! ID de categoría inválido en el parámetro de la URL. Verifica la URL e inténtalo de nuevo.", "invalidMoneySource": "¡Ups! Fuente de dinero inválida. Elige una opción válida e inténtalo de nuevo.", "invalidPaymentMethod": "¡Ups! Método de pago inválido. Selecciona una forma de pago válida.", "invalidCategoryIdentifier": "Identificador de categoría inválido. Verifica los datos e inténtalo de nuevo.", "invalidCategoryType": "Tipo de categoría inválido. Elige un tipo válido e inténtalo de nuevo.", "invalidCategoryBackground": "Color de fondo de categoría inválido. Selecciona un color válido.", "invalidFinancialRecordId": "¡Ups! ID de registro financiero inválido. Verifica los datos e inténtalo de nuevo.", "invalidCategoryId": "¡Ups! ID de categoría inválido. Verifica los datos e inténtalo de nuevo.", "cannotDeleteSystemCategories": "¡Vaya! No puedes eliminar categorías del sistema. ¡Son esenciales para que funcione la app!", "canOnlyDeleteOwnCategories": "Solo puedes eliminar tus propias categorías. ¡Esta no es tuya!", "categoryInUse": "¡Esta categoría está siendo usada en transacciones! Elimina las transacciones primero.", "transactionTypeMismatch": "¡Ups! El tipo de transacción no coincide con el tipo de categoría. Verifica e inténtalo de nuevo.", "invalidTransaction": "¡Ups! Datos de transacción inválidos. Verifica la información e inténtalo de nuevo.", "invalidDreamId": "¡Ups! ID de sueño inválido. Verifica los datos e inténtalo de nuevo.", "transactionNotFound": "Transacción no encontrada. Puede haber sido eliminada o no existe.", "cannotMarkBeforeYesterday": "No puedes marcar 'sin transacciones' para fechas anteriores a ayer. ¡Mantente enfocado en el presente!"}, "category": {"error": {"conflict": "¡Esta categoría ya existe! Elige un nombre diferente.", "createFailed": "¡Ups! No pudimos crear tu categoría. Inténtalo de nuevo.", "findFailed": "¡Ups! No pudimos cargar tus categorías. Inténtalo de nuevo.", "notFound": "Categoría no encontrada. ¿Qué tal crear una nueva?", "invalidId": "ID de categoría inválido. Verifica los datos e inténtalo de nuevo.", "conflictUpdate": "No se pudo actualizar tu categoría. Inténtalo de nuevo.", "updateFailed": "¡Ups! No pudimos guardar los cambios de la categoría. Inténtalo de nuevo.", "deleteFailed": "¡Ups! No pudimos eliminar tu categoría. Inténtalo de nuevo.", "findByIdFailed": "¡Ups! No pudimos encontrar esta categoría. Inténtalo de nuevo.", "findByNameFailed": "¡Ups! No pudimos encontrar la categoría por nombre. Inténtalo de nuevo."}}}, "progression": {"error": {"conflict": "¡Ya tienes progreso guardado! Continúa donde lo dejaste.", "createFailed": "¡Ups! No pudimos guardar tu progreso. Inténtalo de nuevo.", "invalidIdFormat": "ID de progreso inválido. Verifica los datos e inténtalo de nuevo.", "invalidEvent": "Evento de progreso inválido. Verifica los datos e inténtalo de nuevo.", "notFound": "Progreso no encontrado. ¿Qué tal empezar una nueva aventura?", "findFailed": "¡Ups! No pudimos cargar tu progreso. Inténtalo de nuevo.", "notFoundForUser": "Ningún progreso encontrado. ¡Comienza tu viaje de aprendizaje ahora!", "findByUserFailed": "¡Ups! No pudimos cargar tu progreso. Inténtalo de nuevo.", "updateConflict": "No se pudo actualizar tu progreso. Inténtalo de nuevo.", "updateFailed": "¡Ups! No pudimos guardar tus logros. Inténtalo de nuevo.", "notFoundForUpdate": "Progreso no encontrado para actualización. Inténtalo de nuevo.", "deleteFailed": "¡Ups! No pudimos resetear tu progreso. Inténtalo de nuevo.", "notFoundForDeletion": "Progreso no encontrado para eliminación. Puede que ya haya sido removido.", "findTrailProgressionsFailed": "¡Ups! No pudimos cargar tus senderos. Inténtalo de nuevo."}}, "user": {"error": {"hashPassword": "¡Uy! Tuvimos un problemita al guardar tu contraseña. ¿Qué tal si lo intentas de nuevo?", "forbidden": "¡Vaya! Parece que necesitas una llave especial para acceder aquí.", "invalidCredentials": "Mmm, esa combinación de correo y contraseña no parece correcta. ¿Lo intentamos de nuevo?", "resetPassword": "¡Uy! Algo salió mal al intentar restablecer tu contraseña. Por favor, inténtalo una vez más.", "mergeFailed": "¡Oh, oh! Tuvimos un problema al actualizar tu información. ¿Podrías intentarlo de nuevo?", "processPassword": "¡<PERSON><PERSON>, tuvimos un problemita con tu contraseña. ¿Podrías intentarlo de nuevo?", "emailRequired": "¡Uy, falta el correo! Por favor, rellénalo para continuar.", "invalidEmail": "Mmm, ese correo no parece válido. ¿Qué tal si le echas un vistazo?", "emptyId": "¡Uy! Algo inesperado ocurrió de nuestro lado. Por favor, inténtalo de nuevo.", "nameRequired": "¡Casi listo! Solo falta que rellenes tu nombre.", "passwordRequired": "¡No olvides la contraseña! Es superimportante para proteger tu cuenta.", "referralCodeRequired": "¡Falta el código de referido! Rellénalo para continuar.", "setRoleNotAllowed": "¡Vaya! Esa es una acción superpoderosa que no se puede realizar desde aquí.", "phoneRequired": "¡Uy, necesitamos tu número de teléfono para continuar!", "passwordRequirements": "Para una contraseña supersegura, necesita:\n- Al menos 6 caracteres\n- Una letra mayúscula (A-Z)\n- Una letra minúscula (a-z)\n- Un número (0-9)\n- Un carácter especial (!@#$)", "": "", "conflict": "Este usuario ya existe. ¡Intenta con un email diferente!", "notFoundById": "Usuario no encontrado. Verifica si el ID es correcto.", "notFoundByEmail": "Ningún usuario encontrado con este email. ¿Qué tal crear una cuenta?", "notFoundByReferral": "Código de referido inválido. Verifica si lo escribiste correctamente.", "deletedNotFoundByEmail": "Ninguna cuenta eliminada encontrada con este email.", "conflictUpdate": "No se pudo actualizar. Este email ya está siendo usado por otro usuario.", "notFoundForUpdate": "Usuario no encontrado para actualización. Inténtalo de nuevo.", "notFoundForDeletion": "Usuario no encontrado para eliminación. Puede que ya haya sido removido.", "createFailed": "¡Ups! Algo salió mal al crear tu cuenta. Inténtalo de nuevo.", "deletedCreateFailed": "¡Ups! Algo salió mal. Inténtalo de nuevo más tarde.", "findByIdFailed": "¡Ups! Algo salió mal al buscar el usuario. Inténtalo de nuevo.", "findAllFailed": "¡Ups! Algo salió mal al cargar usuarios. Inténtalo de nuevo.", "decodeUserFailed": "¡Ups! Algo salió mal. Inténtalo de nuevo más tarde.", "adminUsersNotFound": "¡Ups! Algo salió mal al buscar administradores. Inténtalo de nuevo.", "accessDenied": "¡Ups! Acceso denegado. No tienes permiso para realizar esta acción.", "decodeAdminUserFailed": "¡Ups! Algo salió mal. Inténtalo de nuevo más tarde.", "findByEmailFailed": "¡Ups! Algo salió mal al buscar el usuario. Inténtalo de nuevo.", "findByReferralFailed": "¡Ups! Algo salió mal al verificar el código. Inténtalo de nuevo.", "findByReferringUserIdFailed": "¡Ups! Algo salió mal. Inténtalo de nuevo más tarde.", "cursorError": "¡Ups! Algo salió mal. Inténtalo de nuevo más tarde.", "findWithFilterFailed": "¡Ups! Algo salió mal al buscar usuarios. Inténtalo de nuevo.", "deletedFindByEmailFailed": "¡Ups! Algo salió mal. Inténtalo de nuevo más tarde.", "invalidId": "ID de usuario inválido. Verifica los datos e inténtalo de nuevo.", "updateFailed": "¡Ups! Algo salió mal al actualizar. Inténtalo de nuevo.", "deleteFailed": "¡Ups! Algo salió mal al eliminar. Inténtalo de nuevo.", "deletedConflictExists": "Esta cuenta ya fue eliminada anteriormente."}}, "dreamboard": {"error": {"conflict": "¡Ya tienes un tablero de sueños! Sigue organizando tus objetivos.", "createFailed": "¡Ups! No pudimos crear tu tablero de sueños. Inténtalo de nuevo.", "findFailed": "¡Ups! No pudimos cargar tu tablero de sueños. Inténtalo de nuevo.", "notFound": "Tablero de sueños no encontrado. ¿Qué tal crear uno nuevo?", "invalidId": "ID del tablero de sueños inválido. Verifica los datos e inténtalo de nuevo.", "findAllFailed": "¡Ups! No pudimos cargar los tableros de sueños. Inténtalo de nuevo.", "decodeFailed": "¡Ups! Algo salió mal al procesar los datos. Inténtalo de nuevo.", "findByUserFailed": "¡Ups! No pudimos encontrar tu tablero de sueños. Inténtalo de nuevo.", "conflictUpdate": "No se pudo actualizar tu tablero de sueños. Inténtalo de nuevo.", "updateFailed": "¡Ups! No pudimos guardar tus cambios. Inténtalo de nuevo.", "deleteFailed": "¡Ups! No pudimos eliminar tu tablero de sueños. Inténtalo de nuevo.", "deleteCreateFailed": "¡Ups! No pudimos mover a la papelera. Inténtalo de nuevo.", "deletedNotFound": "Tablero de sueños eliminado no encontrado. <PERSON>uede que ya haya sido removido.", "deletedFindFailed": "¡Ups! No pudimos encontrarlo en la papelera. Inténtalo de nuevo.", "dreamAddFailed": "¡Ups! No pudimos agregar tu sueño. Inténtalo de nuevo.", "dreamUpdateFailed": "¡Ups! No pudimos actualizar tu sueño. Inténtalo de nuevo.", "dreamNotFound": "Sueño no encontrado. Puede haber sido removido o no existe.", "dreamRemoveFailed": "¡Ups! No pudimos remover el sueño. Inténtalo de nuevo.", "categoryAddFailed": "¡Ups! No pudimos agregar la categoría. Inténtalo de nuevo.", "categoryNotFound": "Categoría no encontrada. Puede haber sido removida o no existe.", "categoryFindFailed": "¡Ups! No pudimos encontrar la categoría. Inténtalo de nuevo.", "categoryUpdateFailed": "¡Ups! No pudimos actualizar la categoría. Inténtalo de nuevo.", "categoryDeleteFailed": "¡Ups! No pudimos eliminar la categoría. Inténtalo de nuevo.", "categoryCreateFailed": "¡Ups! No pudimos crear la categoría. Inténtalo de nuevo.", "sessionStartFailed": "¡Ups! No pudimos iniciar la operación. Inténtalo de nuevo.", "transactionFailed": "¡Ups! No pudimos completar la operación. Inténtalo de nuevo.", "shareLinkConflict": "Este enlace de compartir ya existe. Intenta con un código diferente.", "shareLinkCreateFailed": "¡Ups! No pudimos crear el enlace de compartir. Inténtalo de nuevo.", "shareLinkNotFound": "Enlace de compartir no encontrado. Puede haber expirado o no existe.", "shareLinkFindFailed": "¡Ups! No pudimos encontrar el enlace. Inténtalo de nuevo.", "shareLinkUpdateFailed": "¡Ups! No pudimos actualizar el enlace. Inténtalo de nuevo.", "shareLinkDeleteFailed": "¡Ups! No pudimos eliminar el enlace. Inténtalo de nuevo.", "contributionCreateFailed": "¡Ups! No pudimos registrar tu contribución. Inténtalo de nuevo.", "contributionNotFound": "Contribución no encontrada. Puede haber sido removida o no existe.", "contributionFindFailed": "¡Ups! No pudimos encontrar las contribuciones. Inténtalo de nuevo.", "contributionDecodeFailed": "¡Ups! Algo salió mal al procesar las contribuciones. Inténtalo de nuevo.", "contributionCursorError": "¡Ups! Algo salió mal al cargar las contribuciones. Inténtalo de nuevo.", "contributionUpdateFailed": "¡Ups! No pudimos actualizar la contribución. Inténtalo de nuevo.", "contributionStatusUpdateFailed": "¡Ups! No pudimos actualizar el estado de las contribuciones. Inténtalo de nuevo.", "contributionDeleteFailed": "¡Ups! No pudimos eliminar la contribución. Inténtalo de nuevo.", "categoryIdentifierEmpty": "El identificador de categoría es obligatorio. ¿Qué tal elegir un nombre único?", "categoryNameEmpty": "El nombre de la categoría es obligatorio. ¡Dale un nombre especial!", "categoryIconEmpty": "El ícono de la categoría es obligatorio. ¡Elige un ícono que la represente bien!", "categoryColorEmpty": "El color de la categoría es obligatorio. ¡Elige un color que te guste!", "categoryIdentifierInvalid": "El identificador debe contener solo letras minúsculas y números. ¡Inténtalo de nuevo!", "categoryColorInvalid": "El color debe estar en formato hexadecimal (ej: #FF6347). ¡Verifica e inténtalo de nuevo!", "categoryUnmarshalFailed": "¡Ups! No pudimos procesar los datos de la categoría. Inténtalo de nuevo.", "dreamCategoryInvalid": "La categoría del sueño es inválida. ¡Elige una categoría válida!", "dreamTitleInvalid": "El título debe tener entre 1 y 100 caracteres. ¿Qué tal un título más creativo?", "dreamTimeFrameInvalid": "El plazo seleccionado es inválido. ¡Elige entre corto, mediano o largo plazo!", "dreamDeadlineInvalid": "La fecha límite debe ser al menos 24 horas en el futuro. ¡Elige una fecha posterior!", "dreamCostNegative": "El costo estimado no puede ser negativo. ¡Ingresa un valor válido!", "dreamSavingsInvalid": "El ahorro mensual debe estar entre 0 y el costo total. ¡Ajusta el valor!", "dreamMoneySourceInvalid": "La fuente de dinero seleccionada es inválida. ¡Elige una opción válida!", "dreamCreatorRequired": "El creador del sueño debe especificarse para sueños compartidos.", "dreamFundingStatusInvalid": "El estado de financiamiento es inválido. ¡Verifica los datos!", "dreamRaisedAmountNegative": "El monto recaudado no puede ser negativo. ¡Ingresa un valor válido!", "dreamboardUserRequired": "El usuario del tablero de sueños debe especificarse.", "dreamboardDreamsInvalid": "La lista de sueños es inválida. ¡Verifica los datos!", "dreamboardCategoriesInvalid": "La lista de categorías es inválida. ¡Verifica los datos!", "dreamboardDatesInvalid": "La fecha de creación no puede ser posterior a la fecha de actualización.", "contributionDreamIdRequired": "El ID del sueño es obligatorio para contribuciones.", "contributionUserIdRequired": "El ID del usuario contribuyente es obligatorio.", "contributionAmountNegative": "El monto de contribución mensual no puede ser negativo.", "contributionStatusInvalid": "El estado de la contribución es inválido. ¡Verifica los datos!", "shareLinkDreamIdRequired": "El ID del sueño es obligatorio para enlaces de compartir.", "shareLinkCodeRequired": "El código del enlace debe especificarse.", "shareLinkExpired": "La fecha de expiración debe estar en el futuro.", "invalidInput": "¡Ups! Los datos enviados son inválidos. Verifica e inténtalo de nuevo.", "categoryExists": "¡Esta categoría ya existe! ¿Qué tal elegir un nombre diferente?", "categoryInUse": "Esta categoría está siendo usada por uno o más sueños. ¡Elimina los sueños primero!", "categoryNotInDreamboard": "Esta categoría no existe en tu tablero de sueños.", "alreadyExists": "¡Ya tienes un tablero de sueños! Sigue organizando tus objetivos.", "tokenGenerationFailed": "¡Ups! No pudimos generar el código de invitación. Inténtalo de nuevo.", "inviteLinkDisabled": "Este enlace de invitación está deshabilitado. ¡Solicita un nuevo enlace!", "inviteLinkExpired": "Este enlace de invitación ha expirado. ¡Solicita un nuevo enlace!", "userAlreadyContributing": "¡Ya estás contribuyendo a este sueño! ¡Sigue así!", "missingParam": "Parámetro requerido no encontrado. ¡Verifica los datos!", "invalidType": "Tipo de parámetro inválido. Debe ser 'personal' o 'shared'.", "dreamIdRequired": "El ID del sueño es obligatorio. ¡Verifica los datos!", "contributionIdRequired": "El ID de la contribución es obligatorio. ¡Verifica los datos!", "codeRequired": "El código es obligatorio. ¡Verifica los datos!", "unauthorized": "Solo puedes gestionar tus propios sueños y categorías.", "accessDenied": "Contribución no encontrada o acceso denegado.", "validationRequired": "El código y el monto de contribución mensual son obligatorios."}}, "google": {"error": {"accessTokenRequired": "¡Uy! Tu sesión ha expirado. Por favor, inicia sesión de nuevo.", "InvalidOboardingAgeRange ": "Mmm, esa edad parece un poco extraña. ¿Qué tal si lo intentas de nuevo?", "InvalidOboardingFinancialSituation": "Parece que esa opción no es válida. Por favor, elige una de la lista.", "InvalidOboardingFinancialGoal": "Parece que esa meta no es una opción válida. Por favor, elige una de la lista.", "InvalidOboardingPersonalInterest": "Parece que ese interés no es una opción válida. Por favor, elige uno de la lista.", "FailedToProcessPhoto": "¡Uy! Algo salió mal con tu foto. ¿Probamos con otra?", "InvalidFileType": "Mmm, este tipo de archivo no funciona. Por favor, prueba con una foto en formato JPG, JPEG, PNG o HEIC.", "FileTooLarge": "¡Esa foto es un poco pesada! Intenta con una imagen más pequeña (hasta 5MB).", "InvalidToken": "¡Uy! Tu sesión ha expirado. Por favor, inicia sesión de nuevo para continuar.", "": "", "fetchFailed": "¡Uy! No pudimos conectarnos. Revisa tu conexión a internet e inténtalo de nuevo.", "decodeFailed": "¡Vaya! Tuvimos un problema al procesar tus datos. Inténtalo de nuevo en un momento."}}}