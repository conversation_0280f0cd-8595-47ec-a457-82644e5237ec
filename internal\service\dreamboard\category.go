package dreamboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Category CRUD
func (s *service) CreateCategory(ctx context.Context, board *dreamboard.Dreamboard, category *dreamboard.Category) (*dreamboard.Category, error) {
	if err := category.Validate(); err != nil {
		return nil, err
	}

	// Check for duplicate identifier
	for _, existing := range board.Categories {
		if existing.Identifier == category.Identifier {
			return nil, errors.NewConflictError(errors.Service, "category identifier already exists", errors.KeyDreamboardErrorCategoryExists, nil)
		}
	}

	if err := s.Repository.CreateCategory(ctx, board.ObjectID, category); err != nil {
		return nil, err
	}

	category.ID = category.ObjectID.Hex()
	return category, nil
}

func (s *service) FindCategory(ctx context.Context, board *dreamboard.Dreamboard, categoryID string) (*dreamboard.Category, error) {
	objID, err := primitive.ObjectIDFromHex(categoryID)
	if err != nil {
		return nil, errors.NewWithTranslationKey(errors.Service, "invalid category ID", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, err)
	}

	category, err := s.Repository.FindCategory(ctx, board.ObjectID, objID)
	if err != nil {
		return nil, err
	}

	if category != nil && !category.ObjectID.IsZero() {
		category.ID = category.ObjectID.Hex()
	}

	return category, nil
}

func (s *service) UpdateCategory(ctx context.Context, board *dreamboard.Dreamboard, category *dreamboard.Category) (*dreamboard.Category, error) {
	if err := category.Validate(); err != nil {
		return nil, err
	}

	if category.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(category.ID)
		if err != nil {
			return nil, errors.NewWithTranslationKey(errors.Service, "invalid category ID", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, err)
		}
		category.ObjectID = objID
	}

	// Check for duplicate identifier (excluding the current category)
	for _, existing := range board.Categories {
		if existing.Identifier == category.Identifier && existing.ObjectID != category.ObjectID {
			return nil, errors.NewConflictError(errors.Service, "category identifier already exists", errors.KeyDreamboardErrorCategoryExists, nil)
		}
	}

	if err := s.Repository.UpdateCategory(ctx, board.ObjectID, category); err != nil {
		return nil, err
	}

	category.ID = category.ObjectID.Hex()
	return category, nil
}

func (s *service) DeleteCategory(ctx context.Context, board *dreamboard.Dreamboard, categoryID string) error {
	objID, err := primitive.ObjectIDFromHex(categoryID)
	if err != nil {
		return errors.NewWithTranslationKey(errors.Service, "invalid category ID", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, err)
	}

	// Check if category is being used by any dream
	categoryToDelete, err := s.FindCategory(ctx, board, categoryID)
	if err != nil {
		return err
	}

	for _, dream := range board.Dreams {
		if dream.Category.String() == categoryToDelete.Identifier {
			return errors.NewConflictError(errors.Service, "category is in use by one or more dreams", errors.KeyDreamboardErrorCategoryInUse, nil)
		}
	}

	return s.Repository.DeleteCategory(ctx, board.ObjectID, objID)
}
