package errors

import (
	"errors"
	"testing"
)

func TestDomainError(t *testing.T) {
	originalErr := errors.New("original error")

	// Test New function
	err := New(Service, UserNotFound, NotFound, originalErr)

	if err.Layer() != Service {
		t.<PERSON>rf("Expected layer %s, got %s", Service, err.Layer())
	}

	if err.Kind() != NotFound {
		t.<PERSON>("Expected kind %s, got %s", NotFound, err.Kind())
	}

	if err.TranslationKey() != string(UserNotFound) {
		t.<PERSON>("Expected translation key %s, got %s", string(UserNotFound), err.TranslationKey())
	}

	if err.Unwrap() != originalErr {
		t.<PERSON><PERSON>("Expected original error %v, got %v", originalErr, err.Unwrap())
	}
}

func TestNewWithTranslationKey(t *testing.T) {
	originalErr := errors.New("original error")
	translationKey := "custom_translation_key"

	err := NewWithTranslationKey(Repository, UserNotFound, translationKey, NotFound, originalErr)

	if err.Layer() != Repository {
		t.<PERSON>("Expected layer %s, got %s", Repository, err.Layer())
	}

	if err.Kind() != NotFound {
		t.Errorf("Expected kind %s, got %s", NotFound, err.Kind())
	}

	if err.TranslationKey() != translationKey {
		t.Errorf("Expected translation key %s, got %s", translationKey, err.TranslationKey())
	}

	if err.Unwrap() != originalErr {
		t.Errorf("Expected original error %v, got %v", originalErr, err.Unwrap())
	}
}

func TestOldError(t *testing.T) {
	originalErr := errors.New("original error")

	err := OldError(UserNotFound, NotFound, originalErr)

	if err.Kind() != NotFound {
		t.Errorf("Expected kind %s, got %s", NotFound, err.Kind())
	}

	if err.TranslationKey() != string(UserNotFound) {
		t.Errorf("Expected translation key %s, got %s", string(UserNotFound), err.TranslationKey())
	}

	if err.Unwrap() != originalErr {
		t.Errorf("Expected original error %v, got %v", originalErr, err.Unwrap())
	}
}

func TestDomainErrorString(t *testing.T) {
	// Test without original error
	err := New(Service, UserNotFound, NotFound, nil)
	expected := "service: user not found (kind:not found)"
	if err.Error() != expected {
		t.Errorf("Expected error string %s, got %s", expected, err.Error())
	}

	// Test with original error
	originalErr := errors.New("database connection failed")
	err = New(Repository, UserNotFound, Internal, originalErr)
	expected = "repository: user not found (kind:internal) (cause: database connection failed)"
	if err.Error() != expected {
		t.Errorf("Expected error string %s, got %s", expected, err.Error())
	}
}

func TestHelperFunctions(t *testing.T) {
	originalErr := errors.New("original error")

	tests := []struct {
		name     string
		function func() *DomainError
		kind     Kind
	}{
		{
			name: "NewUserError",
			function: func() *DomainError {
				return NewUserError(Service, UserNotFound, "user_not_found", NotFound, originalErr)
			},
			kind: NotFound,
		},
		{
			name: "NewAuthError",
			function: func() *DomainError {
				return NewAuthError(Service, "invalid credentials", "auth_invalid_credentials", Unauthorized, originalErr)
			},
			kind: Unauthorized,
		},
		{
			name: "NewValidationError",
			function: func() *DomainError {
				return NewValidationError(Controller, "invalid email format", "user_invalid_email", originalErr)
			},
			kind: Validation,
		},
		{
			name: "NewNotFoundError",
			function: func() *DomainError {
				return NewNotFoundError(Repository, UserNotFound, "user_not_found", originalErr)
			},
			kind: NotFound,
		},
		{
			name: "NewConflictError",
			function: func() *DomainError {
				return NewConflictError(Repository, "user already exists", "user_conflict_exists", originalErr)
			},
			kind: Conflict,
		},
		{
			name: "NewInternalError",
			function: func() *DomainError {
				return NewInternalError(Repository, "internal error", "internal", originalErr)
			},
			kind: Internal,
		},
		{
			name: "NewUnauthorizedError",
			function: func() *DomainError {
				return NewUnauthorizedError(Middleware, "unauthorized", "unauthorized", originalErr)
			},
			kind: Unauthorized,
		},
		{
			name: "NewForbiddenError",
			function: func() *DomainError {
				return NewForbiddenError(Middleware, "forbidden", "forbidden", originalErr)
			},
			kind: Forbidden,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.function()

			if err.Kind() != tt.kind {
				t.Errorf("Expected kind %s, got %s", tt.kind, err.Kind())
			}

			if err.Unwrap() != originalErr {
				t.Errorf("Expected original error %v, got %v", originalErr, err.Unwrap())
			}
		})
	}
}

func TestMessageGetMessage(t *testing.T) {
	message := Message("test message")

	if message.GetMessage() != "test message" {
		t.Errorf("Expected 'test message', got %s", message.GetMessage())
	}
}
