package dreamboard

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Dreamboard struct {
	ObjectID        primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID              string             `json:"id,omitempty" bson:"-"`
	User            string             `json:"user" bson:"user"`
	Categories      []*Category        `json:"categories" bson:"categories"`
	Dreams          []*Dream           `json:"dreams" bson:"dreams"`
	TotalDreamsCost monetary.Amount    `json:"totalDreamsCost" bson:"totalDreamsCost"`
	SavedAmount     monetary.Amount    `json:"savedAmount"`
	MonthlyNeeded   monetary.Amount    `json:"monthlyNeeded" bson:"monthlyNeeded"`
	RemainingAmount monetary.Amount    `json:"remainingAmount" bson:"remainingAmount"`
	CreatedAt       time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt       time.Time          `json:"updatedAt" bson:"updatedAt"`
}

func (d *Dreamboard) New() {
	d.CreatedAt = time.Now()
	d.UpdatedAt = time.Now()
}

func (d *Dreamboard) Validate() error {
	if d.User == "" {
		return errors.NewValidationError(errors.Model, "user ID must be specified", errors.KeyDreamboardErrorDreamboardUserRequired, nil)
	}
	if d.Dreams == nil {
		return errors.NewValidationError(errors.Model, "dreams is invalid", errors.KeyDreamboardErrorDreamboardDreamsInvalid, nil)
	}
	if d.Categories == nil {
		return errors.NewValidationError(errors.Model, "categories is invalid", errors.KeyDreamboardErrorDreamboardCategoriesInvalid, nil)
	}
	if d.CreatedAt.After(d.UpdatedAt) {
		return errors.NewValidationError(errors.Model, "createdAt cannot be after updatedAt", errors.KeyDreamboardErrorDreamboardDatesInvalid, nil)
	}

	// Validate all categories
	for _, category := range d.Categories {
		if err := category.Validate(); err != nil {
			return err
		}
	}

	return nil
}

// ComputeTotals calculates and updates the total costs and savings fields
func (d *Dreamboard) ComputeTotals() {
	var totalCost, monthlySum monetary.Amount
	for _, dream := range d.Dreams {
		// Only consider active dreams
		if !dream.Completed {
			totalCost += dream.EstimatedCost
			monthlySum += dream.MonthlySavings
		}
	}
	d.TotalDreamsCost = totalCost
	d.MonthlyNeeded = monthlySum
	d.RemainingAmount = d.SavedAmount - d.TotalDreamsCost

}

// CalculateSavedAmount calculates the saved amount by:
// 1. Summing all "dreams" category transactions from the financial sheet
// 2. Subtracting the transaction value of completed dreams
func (d *Dreamboard) CalculateSavedAmount(financialSheet *financialsheet.Record) {
	var totalDreamMonths monetary.Amount
	var totalRealizedDreams monetary.Amount

	// Sum all "dreams" category transactions from financial sheet
	for _, yearData := range financialSheet.YearData {
		for _, monthData := range yearData {
			for _, category := range monthData.Categories {
				if category.Identifier == "dreams" {
					totalDreamMonths += category.Value
				}
			}
		}
	}

	// Sum only dream transactions that is not completed by the dream id in the transaction
	for _, yearData := range financialSheet.YearData {
		for _, monthData := range yearData {
			for _, transaction := range monthData.Transactions {
				if transaction.Category == financialsheet.CategoryIdentifierDreams {
					// Check if the dream is completed
					for _, dream := range d.Dreams {
						if dream.ID == transaction.AttachedDreamID && dream.Completed {
							totalRealizedDreams += transaction.Value
						}
					}
				}
			}
		}
	}

	d.SavedAmount = totalDreamMonths - totalRealizedDreams
}

// DeletedDreamboard represents a soft-deleted dreamboard entry
type DeletedDreamboard struct {
	Dreamboard  Dreamboard    `json:"dreamboard" bson:"dreamboard"`
	DeletedBy   string        `json:"deletedBy" bson:"deletedBy"`
	Reason      string        `json:"reason" bson:"reason"`
	DeletedAt   time.Time     `json:"deletedAt" bson:"deletedAt"`
	ScheduledAt time.Time     `json:"scheduledAt,omitempty" bson:"scheduledAt,omitempty"`
	RestoredAt  *time.Time    `json:"restoredAt,omitempty" bson:"restoredAt,omitempty"`
	ExpiresAt   time.Time     `json:"expiresAt" bson:"expiresAt"`
	Metadata    []interface{} `json:"metadata" bson:"metadata"`
}
