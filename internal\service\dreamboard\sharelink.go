package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
)

// Share Link Management

// CreateShareLink creates a new share link for a dream
func (s *service) CreateShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error) {
	// Check if share link already exists
	existing, err := s.Repository.FindShareLinkByDreamID(ctx, dreamID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return nil, err
	}
	if existing != nil {
		return existing, nil
	}

	// Generate unique code
	code, err := generateCode()
	if err != nil {
		return nil, errors.NewInternalError(errors.Service, "failed to generate token", errors.KeyDreamboardErrorTokenGenerationFailed, err)
	}

	shareLink := &dreamboard.ShareLink{
		DreamID:   dreamID,
		Code:      code,
		IsEnabled: true,
		ExpiresAt: time.Now().Add(365 * 24 * time.Hour), // 1 year expiration
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := shareLink.Validate(); err != nil {
		return nil, err
	}

	_, err = s.Repository.CreateShareLink(ctx, shareLink)
	if err != nil {
		return nil, err
	}

	shareLink.ID = shareLink.ObjectID.Hex()
	return shareLink, nil
}

// FindShareLink retrieves a share link by dream ID
func (s *service) FindShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error) {
	shareLink, err := s.Repository.FindShareLinkByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	if shareLink != nil && !shareLink.ObjectID.IsZero() {
		shareLink.ID = shareLink.ObjectID.Hex()
	}

	return shareLink, nil
}

// GetShareLinkByToken retrieves a share link by token
func (s *service) FindShareLinkByCode(ctx context.Context, token string) (*dreamboard.ShareLink, error) {
	shareLink, err := s.Repository.FindShareLinkByCode(ctx, token)
	if err != nil {
		return nil, err
	}

	if shareLink != nil && !shareLink.ObjectID.IsZero() {
		shareLink.ID = shareLink.ObjectID.Hex()
	}

	return shareLink, nil
}

// UpdateShareLinkStatus updates the enabled status of a share link
func (s *service) UpdateShareLinkStatus(ctx context.Context, dreamID string, isEnabled bool) error {
	shareLink, err := s.FindShareLink(ctx, dreamID)
	if err != nil {
		return err
	}

	shareLink.IsEnabled = isEnabled
	shareLink.UpdatedAt = time.Now()

	return s.Repository.UpdateShareLink(ctx, shareLink)
}

// RegenerateShareLink creates a new token for an existing share link
func (s *service) RegenerateShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error) {
	shareLink, err := s.FindShareLink(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	// Generate new token
	newCode, err := generateCode()
	if err != nil {
		return nil, errors.NewInternalError(errors.Service, "failed to generate token", errors.KeyDreamboardErrorTokenGenerationFailed, err)
	}

	shareLink.Code = newCode
	shareLink.UpdatedAt = time.Now()

	if err := s.Repository.UpdateShareLink(ctx, shareLink); err != nil {
		return nil, err
	}

	return shareLink, nil
}
