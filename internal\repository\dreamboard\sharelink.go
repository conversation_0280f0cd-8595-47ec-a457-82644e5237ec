package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Share Link CRUD

// CreateShareLink creates a new share link
func (m *mongoDB) CreateShareLink(ctx context.Context, shareLink *dreamboard.ShareLink) (string, error) {
	shareLink.CreatedAt = time.Now()
	shareLink.UpdatedAt = shareLink.CreatedAt

	insertedResult, err := m.shareLinksCollection.InsertOne(ctx, shareLink)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.NewConflictError(errors.Repository, "share link token already exists", errors.KeyDreamboardErrorShareLinkConflict, err)
		}
		return "", errors.NewInternalError(errors.Repository, "failed to create share link", errors.KeyDreamboardErrorShareLinkCreateFailed, err)
	}

	shareLink.ObjectID = insertedResult.InsertedID.(primitive.ObjectID)
	return shareLink.ObjectID.Hex(), nil
}

// FindShareLink retrieves a share link by ID
func (m *mongoDB) FindShareLink(ctx context.Context, id primitive.ObjectID) (*dreamboard.ShareLink, error) {
	var shareLink dreamboard.ShareLink
	err := m.shareLinksCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&shareLink)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "share link not found", errors.KeyDreamboardErrorShareLinkNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find share link", errors.KeyDreamboardErrorShareLinkFindFailed, err)
	}
	return &shareLink, nil
}

// FindShareLinkByCode retrieves a share link by token
func (m *mongoDB) FindShareLinkByCode(ctx context.Context, code string) (*dreamboard.ShareLink, error) {
	var shareLink dreamboard.ShareLink
	err := m.shareLinksCollection.FindOne(ctx, bson.M{"code": code}).Decode(&shareLink)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "share link not found", errors.KeyDreamboardErrorShareLinkNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find share link", errors.KeyDreamboardErrorShareLinkFindFailed, err)
	}
	return &shareLink, nil
}

// FindShareLinkByDreamID retrieves a share link by dream ID
func (m *mongoDB) FindShareLinkByDreamID(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error) {
	var shareLink dreamboard.ShareLink
	err := m.shareLinksCollection.FindOne(ctx, bson.M{"dreamId": dreamID}).Decode(&shareLink)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "share link not found", errors.KeyDreamboardErrorShareLinkNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find share link", errors.KeyDreamboardErrorShareLinkFindFailed, err)
	}
	return &shareLink, nil
}

// UpdateShareLink updates an existing share link
func (m *mongoDB) UpdateShareLink(ctx context.Context, shareLink *dreamboard.ShareLink) error {
	shareLink.UpdatedAt = time.Now()

	filter := bson.M{"_id": shareLink.ObjectID}
	update := bson.M{"$set": shareLink}

	result, err := m.shareLinksCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to update share link", errors.KeyDreamboardErrorShareLinkUpdateFailed, err)
	}

	if result.MatchedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "share link not found", errors.KeyDreamboardErrorShareLinkNotFound, nil)
	}

	return nil
}

// DeleteShareLink deletes a share link by ID
func (m *mongoDB) DeleteShareLink(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.shareLinksCollection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to delete share link", errors.KeyDreamboardErrorShareLinkDeleteFailed, err)
	}

	if result.DeletedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "share link not found", errors.KeyDreamboardErrorShareLinkNotFound, nil)
	}

	return nil
}
