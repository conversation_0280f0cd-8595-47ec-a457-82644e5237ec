package financialsheet

import (
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/stretchr/testify/assert"
)

// TestTranslationKeysExist verifies that all translation keys used in financialsheet service exist
func TestTranslationKeysExist(t *testing.T) {
	// Test that all the translation keys we added are properly defined
	translationKeys := []string{
		// Service layer errors
		errors.KeyFinancialSheetErrorInvalidMonth,
		errors.KeyFinancialSheetErrorDuplicateMonth,
		errors.KeyFinancialSheetErrorSameMonthAsOriginal,
		errors.KeyFinancialSheetErrorRecordAlreadyExists,
		errors.KeyFinancialSheetErrorInvalidRecord,
		errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarked,
		errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarkedDate,
		errors.KeyFinancialSheetErrorCannotMarkSameDayAsTransaction,

		// Controller layer errors
		errors.KeyFinancialSheetErrorInvalidFinancialSheetId,
		errors.KeyFinancialSheetErrorInvalidMonthParameter,
		errors.KeyFinancialSheetErrorInvalidYearParameter,
		errors.KeyFinancialSheetErrorInvalidInput,
		errors.KeyFinancialSheetErrorValidationFailed,
		errors.KeyFinancialSheetErrorDreamTransactionInput,
		errors.KeyFinancialSheetErrorDreamTransactionValidation,
		errors.KeyFinancialSheetErrorInvalidTransactionId,

		// Repository layer errors (already existing)
		errors.KeyFinancialSheetErrorConflict,
		errors.KeyFinancialSheetErrorCreateFailed,
		errors.KeyFinancialSheetErrorFindFailed,
		errors.KeyFinancialSheetErrorFindAllFailed,
		errors.KeyFinancialSheetErrorNotFound,
		errors.KeyFinancialSheetErrorInvalidId,
		errors.KeyFinancialSheetErrorUserNotFound,
		errors.KeyFinancialSheetErrorFindByUserFailed,
		errors.KeyFinancialSheetErrorFindByUsersFailed,
		errors.KeyFinancialSheetErrorConflictUpdate,
		errors.KeyFinancialSheetErrorUpdateFailed,
		errors.KeyFinancialSheetErrorDeleteFailed,
	}

	// Verify that all keys are non-empty strings and follow the expected pattern
	for _, key := range translationKeys {
		assert.NotEmpty(t, key, "Translation key should not be empty")
		assert.Contains(t, key, "financialsheet.error.", "Translation key should contain 'financialsheet.error.'")
	}
}

// TestTranslationKeyFormat verifies that translation keys follow the correct format
func TestTranslationKeyFormat(t *testing.T) {
	tests := []struct {
		name string
		key  string
	}{
		{"Service - Invalid Month", errors.KeyFinancialSheetErrorInvalidMonth},
		{"Service - Duplicate Month", errors.KeyFinancialSheetErrorDuplicateMonth},
		{"Service - Same Month As Original", errors.KeyFinancialSheetErrorSameMonthAsOriginal},
		{"Service - Record Already Exists", errors.KeyFinancialSheetErrorRecordAlreadyExists},
		{"Service - Invalid Record", errors.KeyFinancialSheetErrorInvalidRecord},
		{"Service - No Transactions Already Marked", errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarked},
		{"Service - No Transactions Already Marked Date", errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarkedDate},
		{"Service - Cannot Mark Same Day As Transaction", errors.KeyFinancialSheetErrorCannotMarkSameDayAsTransaction},
		{"Controller - Invalid Financial Sheet ID", errors.KeyFinancialSheetErrorInvalidFinancialSheetId},
		{"Controller - Invalid Month Parameter", errors.KeyFinancialSheetErrorInvalidMonthParameter},
		{"Controller - Invalid Year Parameter", errors.KeyFinancialSheetErrorInvalidYearParameter},
		{"Controller - Invalid Input", errors.KeyFinancialSheetErrorInvalidInput},
		{"Controller - Validation Failed", errors.KeyFinancialSheetErrorValidationFailed},
		{"Controller - Dream Transaction Input", errors.KeyFinancialSheetErrorDreamTransactionInput},
		{"Controller - Dream Transaction Validation", errors.KeyFinancialSheetErrorDreamTransactionValidation},
		{"Controller - Invalid Transaction ID", errors.KeyFinancialSheetErrorInvalidTransactionId},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Verify the key follows the expected pattern: financialsheet.error.specificError
			assert.Regexp(t, `^financialsheet\.error\.[a-zA-Z][a-zA-Z0-9]*$`, tt.key, 
				"Translation key should follow pattern: financialsheet.error.specificError")
		})
	}
}

// TestExpectedTranslationKeyValues verifies that translation keys have the expected values
func TestExpectedTranslationKeyValues(t *testing.T) {
	expectedValues := map[string]string{
		errors.KeyFinancialSheetErrorInvalidMonth:                    "financialsheet.error.invalidMonth",
		errors.KeyFinancialSheetErrorDuplicateMonth:                  "financialsheet.error.duplicateMonth",
		errors.KeyFinancialSheetErrorSameMonthAsOriginal:             "financialsheet.error.sameMonthAsOriginal",
		errors.KeyFinancialSheetErrorRecordAlreadyExists:             "financialsheet.error.recordAlreadyExists",
		errors.KeyFinancialSheetErrorInvalidRecord:                   "financialsheet.error.invalidRecord",
		errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarked:     "financialsheet.error.noTransactionsAlreadyMarked",
		errors.KeyFinancialSheetErrorNoTransactionsAlreadyMarkedDate: "financialsheet.error.noTransactionsAlreadyMarkedDate",
		errors.KeyFinancialSheetErrorCannotMarkSameDayAsTransaction:  "financialsheet.error.cannotMarkSameDayAsTransaction",
		errors.KeyFinancialSheetErrorInvalidFinancialSheetId:         "financialsheet.error.invalidFinancialSheetId",
		errors.KeyFinancialSheetErrorInvalidMonthParameter:           "financialsheet.error.invalidMonthParameter",
		errors.KeyFinancialSheetErrorInvalidYearParameter:            "financialsheet.error.invalidYearParameter",
		errors.KeyFinancialSheetErrorInvalidInput:                    "financialsheet.error.invalidInput",
		errors.KeyFinancialSheetErrorValidationFailed:                "financialsheet.error.validationFailed",
		errors.KeyFinancialSheetErrorDreamTransactionInput:           "financialsheet.error.dreamTransactionInput",
		errors.KeyFinancialSheetErrorDreamTransactionValidation:      "financialsheet.error.dreamTransactionValidation",
		errors.KeyFinancialSheetErrorInvalidTransactionId:            "financialsheet.error.invalidTransactionId",
	}

	for constant, expectedValue := range expectedValues {
		assert.Equal(t, expectedValue, constant, "Translation key constant should have the expected value")
	}
}
