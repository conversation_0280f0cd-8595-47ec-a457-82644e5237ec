package dreamboard

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/labstack/echo/v4"
)

// RegisterContributionRoutes registers contribution related routes
func (dc *controller) RegisterContributionRoutes(ctx context.Context, currentGroup *echo.Group) {
	// // Contributions routes
	// contributionsGroup := currentGroup.Group("/dreamboards/contributions", middlewares.AuthGuard())

	// // Contribution Management
	// contributionsGroup.GET("/dreams/:dreamId", dc.GetContributionsByDreamID())
	// contributionsGroup.GET("/users/me", dc.GetMyContributions())
	// contributionsGroup.PUT("/:contributionId", dc.UpdateContribution())
	// contributionsGroup.DELETE("/:contributionId", dc.DeleteContribution())
	// contributionsGroup.PUT("/dreams/:dreamId/status", dc.UpdateContributionStatus())
}

// FindAllContributions retrieves all contributions for the authenticated user
func (dc *controller) FindAllContributions() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		contributions, err := dc.Service.FindContributionsByUserID(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, contributions)
	}
}

// FindContributionsByDreamID retrieves all contributions for a specific dream
func (dc *controller) FindContributionsByDreamID() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.NewWithTranslationKey(errors.Controller, "dream ID is required", errors.KeyDreamboardErrorDreamIdRequired, errors.BadRequest, nil)
		}

		// Check if user wants only active contributions
		activeOnly := c.QueryParam("active") == "true"

		var contributions []*dreamboard.Contribution
		var err error

		if activeOnly {
			contributions, err = dc.Service.FindActiveContributionsByDreamID(ctx, dreamID)
		} else {
			contributions, err = dc.Service.FindContributionsByDreamID(ctx, dreamID)
		}

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, contributions)
	}
}

// UpdateContribution updates an existing contribution
func (dc *controller) UpdateContribution() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		contributionID := c.Param("contributionId")
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		if contributionID == "" {
			return errors.NewWithTranslationKey(errors.Controller, "contribution ID is required", errors.KeyDreamboardErrorContributionIdRequired, errors.BadRequest, nil)
		}

		var request struct {
			MonthlyPledgedAmount monetary.Amount               `json:"monthlyPledgedAmount"`
			Status               dreamboard.ContributionStatus `json:"status"`
		}

		if err := c.Bind(&request); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid input", errors.KeyDreamboardErrorInvalidInput, err)
		}

		// Get existing contribution to verify ownership
		// This is a simplified approach - in production you might want to add proper authorization
		contributions, err := dc.Service.FindContributionsByUserID(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var targetContribution *dreamboard.Contribution
		for _, contribution := range contributions {
			if contribution.ID == contributionID {
				targetContribution = contribution
				break
			}
		}

		if targetContribution == nil {
			return errors.NewNotFoundError(errors.Controller, "contribution not found or access denied", errors.KeyDreamboardErrorAccessDenied, nil)
		}

		// Update the contribution
		if request.MonthlyPledgedAmount > 0 {
			targetContribution.MonthlyPledgedAmount = request.MonthlyPledgedAmount
		}
		if request.Status != "" {
			targetContribution.Status = request.Status
		}

		err = dc.Service.UpdateContribution(ctx, targetContribution)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, targetContribution)
	}
}

// UpdateContributionStatus updates the status of all contributions for a dream
func (dc *controller) UpdateContributionStatus() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.NewWithTranslationKey(errors.Controller, "dream ID is required", errors.KeyDreamboardErrorDreamIdRequired, errors.BadRequest, nil)
		}

		var request struct {
			Status dreamboard.ContributionStatus `json:"status" validate:"required"`
		}

		if err := c.Bind(&request); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid input", errors.KeyDreamboardErrorInvalidInput, err)
		}

		if !request.Status.IsValid() {
			return errors.NewWithTranslationKey(errors.Controller, "invalid contribution status", errors.KeyDreamboardErrorContributionStatusInvalid, errors.BadRequest, nil)
		}

		err := dc.Service.UpdateContributionStatus(ctx, dreamID, request.Status)
		if err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// DeleteContribution deletes a contribution
func (dc *controller) DeleteContribution() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		contributionID := c.Param("contributionId")
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		if contributionID == "" {
			return errors.NewWithTranslationKey(errors.Controller, "contribution ID is required", errors.KeyDreamboardErrorContributionIdRequired, errors.BadRequest, nil)
		}

		// Verify ownership before deletion
		contributions, err := dc.Service.FindContributionsByUserID(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var hasAccess bool
		for _, contribution := range contributions {
			if contribution.ID == contributionID {
				hasAccess = true
				break
			}
		}

		if !hasAccess {
			return errors.NewNotFoundError(errors.Controller, "contribution not found or access denied", errors.KeyDreamboardErrorAccessDenied, nil)
		}

		err = dc.Service.DeleteContribution(ctx, contributionID)
		if err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}
