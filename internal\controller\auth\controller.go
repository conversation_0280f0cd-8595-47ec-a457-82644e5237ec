package auth

import (
	"context"
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/auth"
	"github.com/dsoplabs/dinbora-backend/internal/service/firebase"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// V2 User Auth
	Register() echo.HandlerFunc

	// User Auth
	Login() echo.HandlerFunc
	LegacyRegister() echo.HandlerFunc
	RefreshToken() echo.HandlerFunc
	ForgotPassword() echo.HandlerFunc
	ResetPassword() echo.HandlerFunc
	CheckPassword() echo.HandlerFunc

	// Admin Auth
	AdminLogin() echo.HandlerFunc

	// HR Auth
	HRLogin() echo.HandlerFunc
}

type controller struct {
	Service         auth.Service
	FirebaseService firebase.Service
}

func New(authService auth.Service, firebaseService firebase.Service) Controller {
	return &controller{
		Service:         authService,
		FirebaseService: firebaseService,
	}
}

// Routes
func (ac *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	legacyAuthGroup := legacyGroup.Group("auth")
	authGroup := currentGroup.Group("/auth")

	// V2 User Auth
	authGroup.POST("/register", ac.Register())

	// User routes
	legacyAuthGroup.POST("/login/", ac.Login())
	legacyAuthGroup.POST("/register/", ac.LegacyRegister())
	legacyAuthGroup.POST("/refreshToken/", ac.RefreshToken())
	legacyAuthGroup.POST("/forgotPassword/", ac.ForgotPassword())
	legacyAuthGroup.POST("/resetPassword/", ac.ResetPassword())
	legacyAuthGroup.POST("/checkPassword/", ac.CheckPassword(), middlewares.AuthGuard())

	// Admin routes
	authGroup.POST("/admin/login", ac.AdminLogin())

	// HR routes
	authGroup.POST("/hr/login", ac.HRLogin())
}

// User Auth
func (ac *controller) Login() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var loginRequest LoginRequestDTO

		if err := c.Bind(&loginRequest); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid login input", errors.KeyAuthErrorInvalidLoginInput, err)
		}
		if err := c.Validate(&loginRequest); err != nil {
			return errors.NewValidationError(errors.Controller, "login validation failed", errors.KeyAuthErrorLoginValidationFailed, err)
		}

		user := &model.User{
			Email:    loginRequest.Email,
			Password: loginRequest.Password,
		}

		user, token, err := ac.Service.Login(ctx, user)
		if err != nil {
			return err
		}

		loginResponse := LoginResponseDTO{
			ID:           user.ID,
			Name:         user.Name,
			LastName:     user.LastName,
			Email:        user.Email,
			PhotoURL:     user.PhotoURL,
			ReferralCode: user.ReferralCode,
			Access:       token.Access,
			Refresh:      token.Refresh,
		}

		if loginRequest.FCMToken != "" {
			err := ac.FirebaseService.Create(ctx, user.ID, loginRequest.FCMToken)
			if err != nil {
				return err
			}
		}

		return c.JSON(http.StatusOK, loginResponse)
	}
}

func (ac *controller) Register() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var user model.User

		referralCode := c.QueryParam("referral")

		// Get FCM token from form
		fcmToken := c.FormValue("fcm")

		if err := c.Bind(&user); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid register input", errors.KeyAuthErrorInvalidRegisterInput, err)
		}

		// Ensure form is parsed if c.Bind() might not have or if it errored early.
		contentType := c.Request().Header.Get(echo.HeaderContentType)
		if c.Request().Form == nil && c.Request().MultipartForm == nil {
			var parseErr error
			if strings.HasPrefix(contentType, echo.MIMEMultipartForm) {
				parseErr = c.Request().ParseMultipartForm(10 << 20)
			} else if strings.HasPrefix(contentType, echo.MIMEApplicationForm) {
				parseErr = c.Request().ParseForm()
			}
			if parseErr != nil {
				return errors.NewValidationError(errors.Controller, "failed to parse form data", errors.KeyAuthErrorFailedToParseFormData, parseErr)
			}
		}

		ageRangeIDStr := c.FormValue("onboarding[ageRange]")
		financialSituationIDStr := c.FormValue("onboarding[financialSituation]")
		financialGoalIDStr := c.FormValue("onboarding[financialGoal]")
		firstPersonalInterestIDStr := c.FormValue("onboarding[personalInterests][0]")

		// Initialize Onboarding only if there's an indication of its data
		if ageRangeIDStr != "" || financialSituationIDStr != "" || financialGoalIDStr != "" || firstPersonalInterestIDStr != "" {
			if user.Onboarding == nil {
				user.Onboarding = new(model.Onboarding)
			}

			if idVal, ok := StringToByte(ageRangeIDStr); ok {
				user.Onboarding.AgeRange.ID = idVal
			} else if ageRangeIDStr != "" { // String was present but not empty and not convertible
				return errors.NewValidationError(errors.Controller, "invalid format for onboarding[ageRange]", errors.KeyAuthErrorInvalidOboardingAgeRange, nil)
			}

			if idVal, ok := StringToByte(financialSituationIDStr); ok {
				user.Onboarding.FinancialSituation.ID = idVal
			} else if financialSituationIDStr != "" {
				return errors.NewValidationError(errors.Controller, "invalid format for onboarding[financialSituation]", errors.KeyAuthErrorInvalidOboardingFinancialSituation, nil)
			}

			if idVal, ok := StringToByte(financialGoalIDStr); ok {
				user.Onboarding.FinancialGoal.ID = idVal
			} else if financialGoalIDStr != "" {
				return errors.NewValidationError(errors.Controller, "invalid format for onboarding[financialGoal]", errors.KeyAuthErrorInvalidOboardingFinancialGoal, nil)
			}

			var interests []model.PersonalInterests // Or model.PersonalInterest
			idx := 0
			for {
				interestKey := fmt.Sprintf("onboarding[personalInterests][%d]", idx)
				interestIDStr := c.FormValue(interestKey)
				if interestIDStr == "" {
					break
				}

				if idVal, ok := StringToByte(interestIDStr); ok {
					interests = append(interests, model.PersonalInterests{ID: idVal})
				} else { // String was present but not empty and not convertible
					errMsg := errors.Message(fmt.Sprintf("invalid format for %s", interestKey))
					return errors.NewValidationError(errors.Controller, errMsg, errors.KeyAuthErrorInvalidOboardingPersonalInterest, nil)
				}
				idx++
				if idx > 50 {
					break
				} // Safety break
			}
			user.Onboarding.PersonalInterests = interests
		}

		// Get photo from request
		photo, err := c.FormFile("photo")
		// If a photo is provided, validate it. If no photo, err will be http.ErrMissingFile, which is fine.
		// If there's another error, it's a problem.
		if err != nil && err != http.ErrMissingFile {
			return errors.NewValidationError(errors.Controller, "failed to process photo", errors.KeyAuthErrorFailedToProcessPhoto, err)
		}

		if photo != nil {
			// Validate file type
			ext := strings.ToLower(filepath.Ext(photo.Filename))
			allowedExts := map[string]bool{
				".jpg":  true,
				".jpeg": true,
				".png":  true,
				".heic": true,
			}
			if !allowedExts[ext] {
				return errors.NewValidationError(errors.Controller, "invalid file type, only JPG, JPEG, and PNG are allowed", errors.KeyAuthErrorInvalidFileType, nil)
			}

			// Validate file size (max 5MB)
			if photo.Size > 5*1024*1024 { // 5MB
				return errors.NewValidationError(errors.Controller, "file too large, max size is 5MB", errors.KeyAuthErrorFileTooLarge, nil)
			}
		} else {
			// Explicitly set photo to nil if it's http.ErrMissingFile, so it's not passed to the service.
			// This handles the case where "photo" form field is entirely absent.
			// If c.FormFile returns an error other than http.ErrMissingFile, we would have returned earlier.
			photo = nil
		}

		tokenData, err := ac.Service.Register(ctx, &user, photo, referralCode)
		if err != nil {
			return err
		}

		// Get user ID from access token
		userToken, err := token.GetClaimsFromToken(tokenData.Access)
		if err != nil {
			return err
		}

		if fcmToken != "" {
			err := ac.FirebaseService.Create(c.Request().Context(), userToken.Uid, fcmToken)
			if err != nil {
				return err
			}
		}

		return c.JSON(http.StatusOK, tokenData)
	}
}

func (ac *controller) LegacyRegister() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var user model.User

		referralCode := c.QueryParam("referral")

		if err := c.Bind(&user); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid register input", errors.KeyAuthErrorInvalidRegisterInput, err)
		}

		accessData, err := ac.Service.LegacyRegister(ctx, &user, referralCode)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, accessData)
	}
}

func (ac *controller) RefreshToken() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		mapToken := map[string]string{}
		if err := c.Bind(&mapToken); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid refresh token input", errors.KeyAuthErrorInvalidRefreshTokenInput, err)
		}
		refreshToken := mapToken["refresh_token"]

		userData, token, err := ac.Service.RefreshAuth(ctx, refreshToken)
		if err != nil {
			return err
		}

		refreshResponse := RefreshResponseDTO{
			ID:           userData.ID,
			Name:         userData.Name,
			LastName:     userData.LastName,
			Email:        userData.Email,
			PhotoURL:     userData.PhotoURL,
			ReferralCode: userData.ReferralCode,
			Access:       token.Access,
			Refresh:      token.Refresh,
		}

		fcmToken := mapToken["fcm"]
		if fcmToken != "" {
			err := ac.FirebaseService.Create(ctx, userData.ID, fcmToken)
			if err != nil {
				return err
			}
		}

		return c.JSON(http.StatusOK, refreshResponse)
	}
}

func (ac *controller) ForgotPassword() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var user model.User

		if err := c.Bind(&user); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid forgot password input", errors.KeyAuthErrorInvalidForgotPasswordInput, err)
		}

		if err := user.PrepareForgotPassword(); err != nil {
			return err
		}

		err := ac.Service.ForgotPassword(ctx, user.Email)
		if err != nil {
			return err
		}

		// Return success response
		return c.JSON(http.StatusOK, map[string]string{
			"message": "Password reset email sent successfully",
		})
	}
}

func (ac *controller) ResetPassword() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var input map[string]interface{}

		if err := c.Bind(&input); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid reset password input", errors.KeyAuthErrorInvalidResetPasswordInput, err)
		}

		if err := ac.Service.ResetPassword(ctx, input["resetToken"].(string), input["password"].(string)); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, nil)
	}
}

func (ac *controller) CheckPassword() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		accessDetails, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		if accessDetails == nil {
			return errors.NewUnauthorizedError(errors.Controller, "user not logged in", errors.KeyAuthErrorUserNotLoggedIn, nil)
		}

		payload := map[string]string{}

		if err = c.Bind(&payload); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid check password input", errors.KeyAuthErrorInvalidCheckPasswordInput, err)
		}

		err = ac.Service.CheckPassword(ctx, accessDetails.Uid, payload["password"])

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, true)
	}
}

// Admin Auth
func (ac *controller) AdminLogin() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var user model.User

		if err := c.Bind(&user); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid admin login input", errors.KeyAuthErrorInvalidAdminLoginInput, err)
		}

		accessData, err := ac.Service.AdminLogin(ctx, &user)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, accessData)
	}
}

func (ac *controller) HRLogin() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var user model.User

		if err := c.Bind(&user); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid hr login input", errors.KeyAuthErrorInvalidHRLoginInput, err)
		}

		accessData, err := ac.Service.HRLogin(ctx, &user)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, accessData)
	}
}

// Helper
// StringToByte converts a string to a byte (uint8).
// Returns the converted byte and a boolean indicating success.
// An empty string is treated as a failed conversion in this version.
func StringToByte(s string) (byte, bool) {
	if s == "" {
		return 0, false // Treat empty string as a conversion failure
	}
	val, err := strconv.ParseUint(s, 10, 8) // Parse as uint8 (byte)
	if err != nil {
		return 0, false // Conversion failed
	}
	return byte(val), true // Conversion successful
}
