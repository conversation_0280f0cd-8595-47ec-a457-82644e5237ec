package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Category Management
func (m mongoDB) CreateCategory(ctx context.Context, boardID primitive.ObjectID, category *dreamboard.Category) error {
	category.ObjectID = primitive.NewObjectID()

	update := bson.D{
		{Key: "$push", Value: bson.D{{Key: "categories", Value: category}}},
		{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
	}

	result, err := m.collection.UpdateByID(ctx, boardID, update)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to add category", errors.KeyDreamboardErrorCategoryAddFailed, err)
	}
	if result.MatchedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "dreamboard not found", errors.KeyDreamboardErrorNotFound, nil)
	}
	return nil
}

func (m mongoDB) CreateCategories(ctx context.Context, boardID primitive.ObjectID, categories []dreamboard.Category) error {
	session, err := m.collection.Database().Client().StartSession()
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to start session", errors.KeyDreamboardErrorSessionStartFailed, err)
	}
	defer session.EndSession(ctx)

	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
		for i := range categories {
			categories[i].ObjectID = primitive.NewObjectID()
			update := bson.D{
				{Key: "$push", Value: bson.D{{Key: "categories", Value: categories[i]}}},
				{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
			}

			result, err := m.collection.UpdateByID(sessCtx, boardID, update)
			if err != nil {
				return nil, errors.NewInternalError(errors.Repository, "failed to add category", errors.KeyDreamboardErrorCategoryAddFailed, err)
			}
			if result.MatchedCount == 0 {
				return nil, errors.NewNotFoundError(errors.Repository, "dreamboard not found", errors.KeyDreamboardErrorNotFound, nil)
			}
		}
		return nil, nil
	}

	_, err = session.WithTransaction(ctx, callback)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to create categories in transaction", errors.KeyDreamboardErrorTransactionFailed, err)
	}

	return nil
}

func (m mongoDB) FindCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) (*dreamboard.Category, error) {
	var dreamboard dreamboard.Dreamboard
	filter := bson.D{
		{Key: "_id", Value: boardID},
		{Key: "categories._id", Value: categoryID},
	}
	opts := options.FindOne().SetProjection(bson.D{
		{Key: "categories.$", Value: 1},
	})

	err := m.collection.FindOne(ctx, filter, opts).Decode(&dreamboard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "category not found", errors.KeyDreamboardErrorCategoryNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find category", errors.KeyDreamboardErrorCategoryFindFailed, err)
	}

	if len(dreamboard.Categories) > 0 {
		return dreamboard.Categories[0], nil
	}

	return nil, errors.NewNotFoundError(errors.Repository, "category not found", errors.KeyDreamboardErrorCategoryNotFound, nil)
}

func (m mongoDB) UpdateCategory(ctx context.Context, boardID primitive.ObjectID, category *dreamboard.Category) error {
	update := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "categories.$[elem]", Value: category},
			{Key: "updatedAt", Value: time.Now()},
		}},
	}

	opts := options.Update().SetArrayFilters(options.ArrayFilters{
		Filters: []interface{}{bson.M{"elem._id": category.ObjectID}},
	})

	result, err := m.collection.UpdateByID(ctx, boardID, update, opts)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to update category", errors.KeyDreamboardErrorCategoryUpdateFailed, err)
	}
	if result.MatchedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "category not found", errors.KeyDreamboardErrorCategoryNotFound, nil)
	}
	return nil
}

func (m mongoDB) DeleteCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) error {
	update := bson.D{
		{Key: "$pull", Value: bson.D{{Key: "categories", Value: bson.D{{Key: "_id", Value: categoryID}}}}},
		{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
	}

	result, err := m.collection.UpdateByID(ctx, boardID, update)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to delete category", errors.KeyDreamboardErrorCategoryDeleteFailed, err)
	}
	if result.MatchedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "category not found", errors.KeyDreamboardErrorCategoryNotFound, nil)
	}
	return nil
}
