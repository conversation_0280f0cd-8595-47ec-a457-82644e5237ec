package dreamboard

import (
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/labstack/echo/v4"
)

// CreateShareLink creates a new share link for a dream
func (dc *controller) CreateShareLink() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.NewWithTranslationKey(errors.Controller, "dream ID is required", errors.KeyDreamboardErrorDreamIdRequired, errors.BadRequest, nil)
		}

		shareLink, err := dc.Service.CreateShareLink(ctx, dreamID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, shareLink)
	}
}

// FindShareLink retrieves a share link by dream ID
func (dc *controller) FindShareLink() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.<PERSON>m("dreamId")

		if dreamID == "" {
			return errors.NewWithTranslationKey(errors.Controller, "dream ID is required", errors.KeyDreamboardErrorDreamIdRequired, errors.BadRequest, nil)
		}

		shareLink, err := dc.Service.FindShareLink(ctx, dreamID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, shareLink)
	}
}

// UpdateShareLinkStatus updates the enabled status of a share link
func (dc *controller) UpdateShareLinkStatus() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.NewWithTranslationKey(errors.Controller, "dream ID is required", errors.KeyDreamboardErrorDreamIdRequired, errors.BadRequest, nil)
		}

		var request struct {
			IsEnabled bool `json:"isEnabled"`
		}

		if err := c.Bind(&request); err != nil {
			return errors.NewValidationError(errors.Controller, "invalid input", errors.KeyDreamboardErrorInvalidInput, err)
		}

		err := dc.Service.UpdateShareLinkStatus(ctx, dreamID, request.IsEnabled)
		if err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// RegenerateShareLink creates a new token for an existing share link
func (dc *controller) RegenerateShareLink() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.NewWithTranslationKey(errors.Controller, "dream ID is required", errors.KeyDreamboardErrorDreamIdRequired, errors.BadRequest, nil)
		}

		shareLink, err := dc.Service.RegenerateShareLink(ctx, dreamID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, shareLink)
	}
}
