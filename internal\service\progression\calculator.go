package progression

import (
	"context"
	"math"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
)

type Calculator struct {
	trailService trail.Service
}

const DefaultPhase = "phase-1"

func NewCalculator(trailService trail.Service) *Calculator {
	return &Calculator{
		trailService: trailService,
	}
}

func (c *Calculator) CalculateProgressFromEvents(ctx context.Context, events []*progression.ProgressEvent, userID string) (*progression.ProgressSummary, error) {
	summary := &progression.ProgressSummary{
		UpdatedAt: time.Now(),
		Revision:  1,
		UserID:    userID,
		//Achievements: make(map[string]*progression.AchievementSummary),
		Trails: make(map[string]*progression.TrailSummary),
	}

	trailEvents := make(map[string][]*progression.ProgressEvent)
	for _, event := range events {
		trailEvents[event.TrailID] = append(trailEvents[event.TrailID], event)
	}

	for trailID, event := range trailEvents {
		trailContent, err := c.trailService.Find(ctx, trailID)
		if err != nil {
			continue
		}

		trailSummary := calculateTrailProgress(event, trailContent)
		summary.Trails[trailID] = trailSummary
	}

	return summary, nil
}

func calculateTrailProgress(events []*progression.ProgressEvent, trailContent *content.Trail) *progression.TrailSummary {
	summary := &progression.TrailSummary{
		ID:               trailContent.ID,
		LessonsCompleted: make(map[string]bool),
		LessonsRewarded:  make(map[string]bool),
		LessonProgress:   make(map[string]*progression.LessonProgress),
	}

	for _, event := range events {
		switch event.ItemType {
		case progression.LessonProgressType:
			processLessonEvent(summary, event)
		case progression.ChallengeProgressType:
			processChallengesEvent(summary, event, trailContent)
		}
	}

	if summary.ChallengeProgress != nil && summary.ChallengeProgress.Phases != nil {
		var points int
		for _, phase := range summary.ChallengeProgress.Phases {
			points += phase.CurrentPoints
		}
		summary.ChallengeProgress.CurrentPoints = points
	}

	calculateProgressPercentage(summary, trailContent)
	return summary
}

func processLessonEvent(summary *progression.TrailSummary, event *progression.ProgressEvent) {
	lessonID := event.ItemID

	if summary.LessonProgress[lessonID] == nil {
		summary.LessonProgress[lessonID] = &progression.LessonProgress{
			Path: make([]string, 0),
		}
	}

	progress := summary.LessonProgress[lessonID]

	switch event.Action {
	case progression.StartedActionType:
		if event.ContentID != "" {
			progress.Path = append(progress.Path, event.ContentID)
			progress.Current = event.ContentID
		}
	case progression.CompletedActionType:
		progress.Completed = true
		summary.LessonsCompleted[lessonID] = true

		if isRewardingChoice(event.Choice) {
			progress.Rewarded = true
			summary.LessonsRewarded[lessonID] = true
			summary.TotalRewards++
		}
	}
}

func processChallengesEvent(summary *progression.TrailSummary, event *progression.ProgressEvent, trailContent *content.Trail) {
	// Initialize challenge if it does not exist
	if summary.ChallengeProgress == nil {
		summary.ChallengeProgress = &progression.ChallengeProgress{
			Phases: make(map[string]*progression.PhaseProgress),
		}
	}

	challenge := summary.ChallengeProgress
	phaseID := event.ItemID

	// Initialize phase if it does not exist
	if challenge.Phases[phaseID] == nil {
		challenge.Phases[phaseID] = &progression.PhaseProgress{
			Path: make([]string, 0),
		}
	}

	phase := challenge.Phases[phaseID]

	switch event.Action {
	case progression.StartedActionType:
		processChallengeStartEvent(phase, challenge, event)
	case progression.CompletedActionType:
		processChallengeCompleteEvent(phase, challenge, event, summary, trailContent)
	}

	if event.Choice.Points != 0 {
		phase.CurrentPoints += event.Choice.Points
	}
}

func processChallengeStartEvent(phase *progression.PhaseProgress, challenge *progression.ChallengeProgress, event *progression.ProgressEvent) {
	if event.ContentID != "" {
		found := false
		for _, contentID := range phase.Path {
			if contentID == event.ContentID {
				found = true
				break
			}
		}

		if !found {
			phase.Path = append(phase.Path, event.ContentID)
		}

		phase.Current = event.ContentID
		challenge.Current = event.ItemID
	}

}

func processChallengeCompleteEvent(phase *progression.PhaseProgress, challenge *progression.ChallengeProgress, event *progression.ProgressEvent, summary *progression.TrailSummary, trailContent *content.Trail) {
	phase.Completed = true

	// Check if all phases from the trail content are completed
	completedAllPhases := areAllChallengesPhasesCompleted(challenge, trailContent)

	if completedAllPhases {
		challenge.Completed = true
		summary.ChallengeCompleted = true

		if isRewardingChoice(event.Choice) {
			challenge.Rewarded = true
			summary.ChallengeRewarded = true
			summary.TotalRewards++
		}
	}
}

// New helper function to properly check if all phases are completed
func areAllChallengesPhasesCompleted(challengeProgress *progression.ChallengeProgress, trailContent *content.Trail) bool {
	if trailContent.Challenge == nil {
		return false
	}

	// Check each phase defined in the trail content
	for _, phaseContent := range trailContent.Challenge.Phases {
		phaseProgress, exists := challengeProgress.Phases[phaseContent.Identifier]
		if !exists || !phaseProgress.Completed {
			return false
		}
	}

	return true
}

func calculateProgressPercentage(summary *progression.TrailSummary, trailContent *content.Trail) {
	// Count lessons
	totalContent := len(trailContent.Lessons)
	if trailContent.HasChallenge() {
		totalContent++
	}

	completedContent := len(summary.LessonsCompleted)
	if summary.ChallengeCompleted {
		completedContent++
	}

	progress := (completedContent * 100) / totalContent

	if trailContent.HasChallenge() && !summary.ChallengeCompleted && progress >= 100 {
		progress = 90 // Max progress is 90 if challenge is not completed!
	}

	summary.ProgressPercent = int(math.Min(float64(progress), 100))
	summary.IsCompleted = progress >= 100
}

func isRewardingChoice(choice *progression.Choice) bool {
	return choice != nil && choice.Next == string(progression.RewardTypeCoin)
}
