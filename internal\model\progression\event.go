package progression

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ProgressType string
type ActionType string

const (
	LessonProgressType    ProgressType = "LESSON"
	ChallengeProgressType ProgressType = "CHALLENGE"
)

const (
	StartedActionType   ActionType = "STARTED"
	CompletedActionType ActionType = "COMPLETED"
)

// ProgressEvent represents an event that occurs in the progression of a user.
type ProgressEvent struct {
	ID        primitive.ObjectID     `json:"-" bson:"_id,omitempty"`
	UserID    string                 `json:"userId" bson:"userId"`
	TrailID   string                 `json:"trailId" bson:"trailId"`
	ItemID    string                 `json:"itemId" bson:"itemId"`
	ContentID string                 `json:"contentId" bson:"contentId"`
	ItemType  ProgressType           `json:"itemType" bson:"itemType"`
	Action    ActionType             `json:"action" bson:"action"`
	Choice    *Choice                `json:"choice,omitempty" bson:"choice,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty" bson:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp" bson:"timestamp"`

	// Migration fields
	LegacyID string `json:"legacyId,omitempty" bson:"legacyId,omitempty"`
	Migrated bool   `json:"migrated" bson:"migrated"`
}

// Choice represents a choice made by the user.
type Choice struct {
	Identifier string `json:"identifier" bson:"identifier"`
	Points     int    `json:"points" bson:"points"`
	Next       string `json:"next" bson:"next"`
}

func (p ProgressType) IsValid() bool {
	switch p {
	case LessonProgressType, ChallengeProgressType:
		return true
	default:
		return false
	}
}

func (a ActionType) IsValid() bool {
	switch a {
	case StartedActionType, CompletedActionType:
		return true
	default:
		return false
	}
}

func (e *ProgressEvent) IsValid() bool {
	if !isValidObjectID(e.UserID) || !isValidObjectID(e.TrailID) {
		return false
	}

	if e.ItemID == "" {
		return false
	}

	if !e.ItemType.IsValid() || !e.Action.IsValid() {
		return false
	}
	return true
}

func isValidObjectID(id string) bool {
	_, err := primitive.ObjectIDFromHex(id)
	return err == nil
}

func (e *ProgressEvent) SetDefaults() {
	if e.Timestamp.IsZero() {
		e.Timestamp = time.Now()
	}
	if e.Data == nil {
		e.Data = make(map[string]interface{})
	}
}
