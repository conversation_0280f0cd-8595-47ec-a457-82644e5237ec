package aiassistant

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/service/aiassistant"
	"github.com/labstack/echo/v4"
)

// Controller defines the interface for the AI Assistant controller
type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// Handlers
	FetchForUser() echo.HandlerFunc
}

// controller implements the Controller interface
type controller struct {
	Service aiassistant.Service
}

// New creates a new instance of the AI Assistant controller
func New(service aiassistant.Service) Controller {
	return &controller{
		Service: service,
	}
}

// RegisterRoutes registers all routes for the AI Assistant controller
func (c *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	// Create a group for AI assistant endpoints
	aiAssistantGroup := currentGroup.Group("/aiassistant")

	// Register routes with service authentication middleware
	aiAssistantGroup.GET("/users/:userId/context", c.FetchForUser(), middlewares.ServiceAuthMiddleware())
	aiAssistantGroup.GET("/users/:userId/context", c.FetchForUser(), middlewares.AuthGuard())
}

// FetchForUser handles the GET /v2/aiassistant/users/:userId/context endpoint
func (c *controller) FetchForUser() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Extract user ID from path
		userID := e.Param("userId")
		if userID == "" {
			return errors.New(errors.Controller, "missing user ID", errors.BadRequest, nil)
		}

		// Call service to fetch AI context data
		aiContext, err := c.Service.FetchForUser(ctx, userID)
		if err != nil {
			return err
		}

		// Return the AI context data
		return e.JSON(http.StatusOK, aiContext)
	}
}
