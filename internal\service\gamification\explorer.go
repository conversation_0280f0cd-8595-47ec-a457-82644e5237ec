package gamification

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

// Explorou Oportunidades implementation
func (s *service) CheckExplorerAchievement(ctx context.Context, userID string) error {
	// Step 1: Check if user already has Explorer achievement (fail-fast)
	hasAchievement, err := s.Repository.HasAchievement(ctx, userID, explorerAchievementIdentifier)
	if err != nil {
		return errors.New(errors.Service, "failed to check if user has explorer achievement", errors.Internal, err)
	}
	if hasAchievement {
		// User already has the achievement, no need to check further
		return nil
	}

	// Step 2: Check if user completed "diagnostico-inicial-phase001" challenge (fail-fast)
	hasCompletedTrails, err := s.hasCompletedDiagnosticoRapidoChallenge(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check diagnostico financeiro challenge completion", errors.Internal, err)
	}
	if !hasCompletedTrails {
		// Challenge not completed, user doesn't qualify for Explorer achievement
		return nil
	}

	// Step 3: Check if user completed "calcule-sua-reserva-phase001" challenge (fail-fast)
	hasCompletedTrails, err = s.hasCompletedCalculeSuaReservaChallenge(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check calcule sua reserva challenge completion", errors.Internal, err)
	}
	if !hasCompletedTrails {
		// Challenge not completed, user doesn't qualify for Explorer achievement
		return nil
	}

	// Step 4: Check if user completed "apontamento-financeiro-phase001" challenge (fail-fast)
	hasCompletedTrails, err = s.hasCompletedApontamentoFinanceiroChallenge(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check apontamento financeiro challenge completion", errors.Internal, err)
	}
	if !hasCompletedTrails {
		// Challenge not completed, user doesn't qualify for Explorer achievement
		return nil
	}

	// Step 5: Check if user has recorded at least 30 points in the Financial Sheet
	hasCompletedTrails, err = s.hasRecorded30Points(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check financial sheet points", errors.Internal, err)
	}
	if !hasCompletedTrails {
		// User hasn't recorded 30 points, doesn't qualify for Explorer achievement
		return nil
	}

	// Step 6: All checks passed, award the achievement
	return s.awardAchievement(ctx, userID, explorerAchievementIdentifier)
}

// Helper methods to award an achievement
func (s *service) hasCompletedDiagnosticoRapidoChallenge(ctx context.Context, userID string) (bool, error) {
	progressionSummary, err := s.ProgressionRepo.GetProgressSummary(ctx, userID)
	if err != nil {
		return false, err
	}

	// Look for "diagnostico-inicial-phase001" phase completion in any trail
	return s.hasCompletedChallengePhase(progressionSummary, explorerChallengePhase001Identifier), nil
}

func (s *service) hasCompletedCalculeSuaReservaChallenge(ctx context.Context, userID string) (bool, error) {
	progressionSummary, err := s.ProgressionRepo.GetProgressSummary(ctx, userID)
	if err != nil {
		return false, err
	}

	// Look for "calcule-sua-reserva-phase001" phase completion in any trail
	return s.hasCompletedChallengePhase(progressionSummary, explorerChallengePhase002Identifier), nil
}

func (s *service) hasCompletedApontamentoFinanceiroChallenge(ctx context.Context, userID string) (bool, error) {
	progressionSummary, err := s.ProgressionRepo.GetProgressSummary(ctx, userID)
	if err != nil {
		return false, err
	}

	// Look for "apontamento-financeiro-phase001" phase completion in any trail
	return s.hasCompletedChallengePhase(progressionSummary, explorerChallengePhase003Identifier), nil
}

func (s *service) hasRecorded30Points(ctx context.Context, userID string) (bool, error) {
	financialSheet, err := s.FinancialSheetRepo.FindByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	return financialSheet.Points.Best >= 30, nil
}
