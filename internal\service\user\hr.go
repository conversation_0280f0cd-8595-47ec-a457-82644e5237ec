package user

import (
	"context"
	"math"
	"sort"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
)

// FindEmployeesByHR retrieves all employees that were referred by the specified HR user
// This method uses the RBAC service to build appropriate filters and validate permissions
// It leverages the centralized RBAC logic for consistency with other role-based endpoints
func (s *service) FindEmployeesByHR(ctx context.Context, hrUserID string, hrUserRoles []string) ([]*model.User, error) {
	// Use RBAC service to build the appropriate filter for HR employee access
	// This handles role validation and creates the MongoDB filter for the referral relationship
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

	// Query the repository using the RBAC-generated filter
	// This ensures consistent access control patterns across the application
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Return the list of employees (could be empty if no employees found)
	return employees, nil
}

// FindTopEngagers retrieves the top engagers for the HR user's company based on the number of sequence, achievements and dcoins
func (s *service) FindTopEngagers(ctx context.Context, hrUserID string, hrUserRoles []string) ([]*Employee, error) {
	// Use RBAC service to build the appropriate filter for HR employee access
	// This handles role validation and creates the MongoDB filter for the referral relationship
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

	// Query the repository using the RBAC-generated filter
	// This ensures consistent access control patterns across the application
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Get the IDs of all employees for achievements and vaults lookups
	employeesIDs := make([]string, len(employees))
	for i, employee := range employees {
		employeesIDs[i] = employee.ID
	}

	// Get number of sequence (current points in the financial sheet)
	financialSheets, err := s.FinancialSheetService.FindByUsers(ctx, employeesIDs)
	if err != nil {
		return nil, err
	}

	// Get number of dcoins
	vaults, err := s.VaultService.FindVaultsByUsers(ctx, employeesIDs)
	if err != nil {
		return nil, err
	}

	// Get number of achievements
	achievements, err := s.GamificationService.FindUsersAchievements(ctx, employeesIDs)
	if err != nil {
		return nil, err
	}

	// Create a list of top engagers
	var topEngagers []*Employee

	for _, employee := range employees {
		engager := &Employee{
			Name:         employee.Name,
			Email:        employee.Email,
			Sequence:     financialSheets[employee.ID].Points.Current,
			DCoins:       int(vaults[employee.ID].Coins),
			Achievements: len(achievements[employee.ID]),
		}

		// Add to list
		topEngagers = append(topEngagers, engager)
	}

	// Sort employees by number of sequence, achievements and dcoins
	// First by sequence, then by achievements, then by dcoins
	sort.Slice(topEngagers, func(i, j int) bool {
		if topEngagers[i].Sequence == topEngagers[j].Sequence {
			if topEngagers[i].Achievements == topEngagers[j].Achievements {
				return topEngagers[i].DCoins > topEngagers[j].DCoins
			}
			return topEngagers[i].Achievements > topEngagers[j].Achievements
		}
		return topEngagers[i].Sequence > topEngagers[j].Sequence
	})

	// Return the list of top engagers
	return topEngagers, nil
}

// FindJourneyProgression returns aggregated journey progression for the HR user's company
func (s *service) FindJourneyProgression(ctx context.Context, hrUserID string, hrUserRoles []string) ([]*JourneyProgression, error) {
	// RBAC filter
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

	// Employees
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	// IDs
	employeeIDs := make([]string, len(employees))
	for i, e := range employees {
		employeeIDs[i] = e.ID
	}

	// Progressions
	progressions, err := s.ProgressionService.GetUsersProgress(ctx, employeeIDs)
	if err != nil {
		return nil, err
	}

	// Aggregate by stage
	stageTotals := map[string]struct {
		completed  int
		total      int
		percentage float64
	}{
		"Início da Jornada": {0, 0, 0.0},
		"Diagnosticar":      {0, 0, 0.0},
		"Sonhar":            {0, 0, 0.0},
		"Orçar":             {0, 0, 0.0},
		"Poupar":            {0, 0, 0.0},
	}

	// For each user, count the number of completed trails in each stage
	for _, prog := range progressions {
		for _, trail := range prog.Trails {
			stage := s.getTrailName(trail.ID)
			stats := stageTotals[stage]

			stats.total++
			if trail.IsCompleted {
				stats.completed++
			}
			stats.percentage = stats.percentage + float64(trail.ProgressPercent)

			stageTotals[stage] = stats
		}
	}

	// Build list
	var journeyProgressions []*JourneyProgression
	order := []string{"Início da Jornada", "Diagnosticar", "Sonhar", "Orçar", "Poupar"}

	// Among all users, calculate the percentage of completed trails for each stage
	// Users that don't have a progression didn't returned in the progression and SHOULD be considered
	// We should consider the total of the users in the company, not just the ones with a progression
	// Example: 100 users, 80 with progression in a stage, 20 without. 80% completion rate.
	for _, stage := range order {
		stats := stageTotals[stage]
		percentage := 0.0
		percentage = stats.percentage / float64(len(employees))

		journeyProgressions = append(journeyProgressions, &JourneyProgression{
			Name:       stage,
			Percentage: math.Round(percentage*100) / 100, // Trunk the percentage to 2 decimal places
		})
	}

	return journeyProgressions, nil
}

// FindDreamsProgression returns the dreams progress for the HR user's company based on the number of registered dreams and completed dreams
func (s *service) FindDreamsProgression(ctx context.Context, hrUserID string, hrUserRoles []string) (*DreamsProgression, error) {
	// RBAC filter
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

	// Employees
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	// IDs
	employeeIDs := make([]string, len(employees))
	for i, e := range employees {
		employeeIDs[i] = e.ID
	}

	// Dreams
	dreams, err := s.DreamboardService.FindAllByUsers(ctx, employeeIDs)
	if err != nil {
		return nil, err
	}

	// Count
	registered := 0
	completed := 0
	for _, dreamboard := range dreams {
		registered += len(dreamboard.Dreams)
		for _, dream := range dreamboard.Dreams {
			if dream.Completed {
				completed++
			}
		}
	}

	// Calculate percentage
	percentage := 0.0
	if registered > 0 {
		percentage = float64(completed) / float64(registered) * 100
	}

	return &DreamsProgression{
		Registered: registered,
		Completed:  completed,
		Progress:   math.Round(percentage*10) / 10, // Trunk the percentage to 1 decimal places
	}, nil
}

// FindFinancialProfileDistribution returns the personal financial statement distribution
// for the HR user's company based on the user's pontuation in the "perfil-financeiro" challenge phase points
func (s *service) FindFinancialProfileDistribution(ctx context.Context, hrUserID string, hrUserRoles []string) (*FinancialProfileDistribution, error) {
	// RBAC filter
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

	// Employees
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Caculate the percentage of users in each financial profile
	// Count
	undefined := 0
	investor := 0
	balanced := 0
	indebted := 0
	overindebted := 0

	for _, employee := range employees {
		switch employee.FinancialProfile.Status {
		case model.StatusUndefined:
			undefined++
		case model.StatusInvestor:
			investor++
		case model.StatusBalanced:
			balanced++
		case model.StatusIndebted:
			indebted++
		case model.StatusOverindebted:
			overindebted++
		}
	}

	// Calculate percentage
	total := len(employees)
	undefinedPercentage := float64(undefined) / float64(total) * 100
	investorPercentage := float64(investor) / float64(total) * 100
	balancedPercentage := float64(balanced) / float64(total) * 100
	indebtedPercentage := float64(indebted) / float64(total) * 100
	overindebtedPercentage := float64(overindebted) / float64(total) * 100

	return &FinancialProfileDistribution{
		Undefined:    math.Round(undefinedPercentage*100) / 100,    // Trunk the percentage to 2 decimal places (divided by 100 to get the percentage)
		Investor:     math.Round(investorPercentage*100) / 100,     // Trunk the percentage to 2 decimal places (divided by 100 to get the percentage)
		Balanced:     math.Round(balancedPercentage*100) / 100,     // Trunk the percentage to 2 decimal places (divided by 100 to get the percentage)
		Indebted:     math.Round(indebtedPercentage*100) / 100,     // Trunk the percentage to 2 decimal places (divided by 100 to get the percentage)
		Overindebted: math.Round(overindebtedPercentage*100) / 100, // Trunk the percentage to 2 decimal places (divided by 100 to get the percentage)
	}, nil

}

// FindAgeRanges returns the age ranges for the HR user's company based on the user's age
func (s *service) FindAgeRanges(ctx context.Context, hrUserID string, hrUserRoles []string) (*AgeRanges, error) {
	// RBAC filter
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

	// Employees
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Count
	lessThan12 := 0
	from12To17 := 0
	from18To29 := 0
	from30To59 := 0
	greater60 := 0

	for _, employee := range employees {
		age := employee.Onboarding.AgeRange.ID
		switch age {
		case model.AgeRange0To11:
			lessThan12++
		case model.AgeRange12To17:
			from12To17++
		case model.AgeRange18To29:
			from18To29++
		case model.AgeRange30To59:
			from30To59++
		case model.AgeRange60Plus:
			greater60++
		}
	}

	// Return the age ranges
	return &AgeRanges{
		LessThan12: lessThan12,
		From12To17: from12To17,
		From18To29: from18To29,
		From30To59: from30To59,
		Greater60:  greater60,
	}, nil
}

// FindFinancialSituations returns the financial situations for the HR user's company based on the user's financial situation
func (s *service) FindFinancialSituations(ctx context.Context, hrUserID string, hrUserRoles []string) (*FinancialSituations, error) {
	// RBAC filter
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

	// Employees
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Count
	cantPayBills := 0
	inDebtButManaging := 0
	noDebtButCantSave := 0
	savesMonthly := 0
	investsAndWantsMore := 0

	for _, employee := range employees {
		financialSituation := employee.Onboarding.FinancialSituation.ID
		switch financialSituation {
		case model.CantPayBills:
			cantPayBills++
		case model.InDebtButManaging:
			inDebtButManaging++
		case model.NoDebtButCantSave:
			noDebtButCantSave++
		case model.SavesMonthly:
			savesMonthly++
		case model.InvestsAndWantsMore:
			investsAndWantsMore++
		}
	}

	// Calculate percentage
	total := len(employees)
	cantPayBillsPercentage := float64(cantPayBills) / float64(total) * 100
	inDebtButManagingPercentage := float64(inDebtButManaging) / float64(total) * 100
	noDebtButCantSavePercentage := float64(noDebtButCantSave) / float64(total) * 100
	savesMonthlyPercentage := float64(savesMonthly) / float64(total) * 100
	investsAndWantsMorePercentage := float64(investsAndWantsMore) / float64(total) * 100

	return &FinancialSituations{
		CantPayBills:        math.Round(cantPayBillsPercentage*100) / 100,        // Trunk the percentage to 2 decimal places (divided by 100 to get the percentage)
		InDebtButManaging:   math.Round(inDebtButManagingPercentage*100) / 100,   // Trunk the percentage to 2 decimal places (divided by 100 to get the percentage)
		NoDebtButCantSave:   math.Round(noDebtButCantSavePercentage*100) / 100,   // Trunk the percentage to 2 decimal places (divided by 100 to get the percentage)
		SavesMonthly:        math.Round(savesMonthlyPercentage*100) / 100,        // Trunk the percentage to 2 decimal places (divided by 100 to get the percentage)
		InvestsAndWantsMore: math.Round(investsAndWantsMorePercentage*100) / 100, // Trunk the percentage to 2 decimal places (divided by 100 to get the percentage)
	}, nil
}

// FindFinancialProfileEvolution returns the monthly financial profile evolution for a company.
// It tracks the distribution of financial profiles from the month the first employee
// got a profile until the current month using complete history records.
func (s *service) FindFinancialProfileEvolution(ctx context.Context, hrUserID string, hrUserRoles []string) ([]*MonthlyFinancialProfileEvolution, error) {
	// Get employees for the company
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	if len(employees) == 0 {
		return []*MonthlyFinancialProfileEvolution{}, nil
	}

	// Extract user IDs for history lookup
	userIDs := make([]string, len(employees))
	for i, employee := range employees {
		userIDs[i] = employee.ID
	}

	// Get all financial profile history for these users
	histories, err := s.Repository.FindFinancialProfileHistoryByUsers(ctx, userIDs)
	if err != nil {
		return nil, err
	}

	if len(histories) == 0 {
		return []*MonthlyFinancialProfileEvolution{}, nil
	}

	// Define a canonical order to ensure consistent output for the graph.
	financialProfileOrder := []string{
		"Undefined",
		"Overindebted",
		"Indebted",
		"Balanced",
		"Investor",
	}

	// Find the start date for the timeline by finding the earliest history record.
	var startDate time.Time
	for _, history := range histories {
		if startDate.IsZero() || history.CreatedAt.Before(startDate) {
			startDate = history.CreatedAt
		}
	}

	// If no history records exist, return empty.
	if startDate.IsZero() {
		return []*MonthlyFinancialProfileEvolution{}, nil
	}

	// Normalize all time calculations to UTC to avoid timezone-related bugs.
	// The timeline starts at the beginning of the month of the earliest profile.
	startDate = time.Date(startDate.Year(), startDate.Month(), 1, 0, 0, 0, 0, time.UTC)

	// The loop will run up to, but not including, the first day of the next month.
	now := time.Now().UTC()
	currentMonthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC)
	loopEndDate := currentMonthStart.AddDate(0, 1, 0)

	// Iterate through each month and build the historical data
	monthlyEvolutions := make(map[string]*MonthlyFinancialProfileEvolution)

	for t := startDate; t.Before(loopEndDate); t = t.AddDate(0, 1, 0) {
		monthKey := t.Format("2006-01")
		endOfMonth := t.AddDate(0, 1, 0).Add(-time.Nanosecond)

		// Pre-populate map to guarantee all profiles are present in the output.
		profileCounts := make(map[string]int)
		for _, profileName := range financialProfileOrder {
			profileCounts[profileName] = 0
		}

		totalEmployeesThisMonth := 0

		// For each month, calculate the profile distribution at that point in time using history.
		for _, employee := range employees {
			// Find the most recent status for this employee up to the end of this month
			latestStatus := s.getLatestStatusForMonth(histories, employee.ID, endOfMonth)
			if latestStatus != "" {
				if _, ok := profileCounts[latestStatus]; ok {
					profileCounts[latestStatus]++
				}
				totalEmployeesThisMonth++
			}
		}

		if totalEmployeesThisMonth > 0 {
			// Build the datapoints slice based on the defined order for consistency.
			datapoints := make([]*FinancialProfileDatapoint, 0, len(financialProfileOrder))
			for _, profileName := range financialProfileOrder {
				count := profileCounts[profileName]
				percentage := 0.0
				if totalEmployeesThisMonth > 0 {
					percentage = float64(count) * 100 / float64(totalEmployeesThisMonth)
				}
				datapoints = append(datapoints, &FinancialProfileDatapoint{
					FinancialProfile: profileName,
					Count:            count,
					Percentage:       math.Round(percentage*100) / 100,
				})
			}

			monthlyEvolutions[monthKey] = &MonthlyFinancialProfileEvolution{
				Month:      monthKey,
				Datapoints: datapoints,
			}
		}
	}

	// Convert the map to a sorted slice to ensure months are in chronological order.
	keys := make([]string, 0, len(monthlyEvolutions))
	for k := range monthlyEvolutions {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	result := make([]*MonthlyFinancialProfileEvolution, 0, len(keys))
	for _, k := range keys {
		result = append(result, monthlyEvolutions[k])
	}

	return result, nil
}

// FindPersonalInterests returns the personal interests for the HR user's company based on the user's personal interests
func (s *service) FindPersonalInterests(ctx context.Context, hrUserID string, hrUserRoles []string) (*PersonalInterests, error) {
	// RBAC filter
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

	// Employees
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Count
	fashion := 0
	family := 0
	pets := 0
	travel := 0
	nature := 0
	music := 0
	selfCare := 0
	books := 0
	shopping := 0
	movies := 0
	games := 0
	fitness := 0
	technology := 0
	languages := 0
	astrology := 0
	food := 0
	work := 0
	culture := 0
	sports := 0
	spaceExploration := 0
	crypto := 0
	health := 0

	for _, employee := range employees {
		for _, interest := range employee.Onboarding.PersonalInterests {
			switch interest.ID {
			case model.Fashion:
				fashion++
			case model.Family:
				family++
			case model.Pets:
				pets++
			case model.Travel:
				travel++
			case model.Nature:
				nature++
			case model.Music:
				music++
			case model.SelfCare:
				selfCare++
			case model.Books:
				books++
			case model.Shopping:
				shopping++
			case model.Movies:
				movies++
			case model.Games:
				games++
			case model.Fitness:
				fitness++
			case model.Technology:
				technology++
			case model.Languages:
				languages++
			case model.Astrology:
				astrology++
			case model.Food:
				food++
			case model.Work:
				work++
			case model.Culture:
				culture++
			case model.Sports:
				sports++
			case model.SpaceExploration:
				spaceExploration++
			case model.Crypto:
				crypto++
			case model.Health:
				health++
			}
		}
	}

	return &PersonalInterests{
		Fashion:          fashion,
		Family:           family,
		Pets:             pets,
		Travel:           travel,
		Nature:           nature,
		Music:            music,
		SelfCare:         selfCare,
		Books:            books,
		Shopping:         shopping,
		Movies:           movies,
		Games:            games,
		Fitness:          fitness,
		Technology:       technology,
		Languages:        languages,
		Astrology:        astrology,
		Food:             food,
		Work:             work,
		Culture:          culture,
		Sports:           sports,
		SpaceExploration: spaceExploration,
		Crypto:           crypto,
		Health:           health,
	}, nil
}

// FindFinancialGoals returns the financial goals for the HR user's company based on the user's financial goals
func (s *service) FindFinancialGoals(ctx context.Context, hrUserID string, hrUserRoles []string) (*FinancialGoals, error) {
	// RBAC filter
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

	// Employees
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Count
	debtControl := 0
	moneySavings := 0
	strategicFund := 0
	futureInvestments := 0
	financialLiteracy := 0

	for _, employee := range employees {
		goal := employee.Onboarding.FinancialGoal.ID
		switch goal {
		case model.DebtControl:
			debtControl++
		case model.MoneySavings:
			moneySavings++
		case model.StrategicFund:
			strategicFund++
		case model.FutureInvestments:
			futureInvestments++
		case model.FinancialLiteracy:
			financialLiteracy++
		}
	}

	return &FinancialGoals{
		DebtControl:       debtControl,
		MoneySavings:      moneySavings,
		StrategicFund:     strategicFund,
		FutureInvestments: futureInvestments,
		FinancialLiteracy: financialLiteracy,
	}, nil
}

// Helper

func (s *service) getTrailName(trailID string) string {
	switch trailID {
	case "67f6ddf3181babca8896e73c":
		return "Início da Jornada"
	case "67fd63f5886e2c50f312b910":
		return "Diagnosticar"
	case "67fd63fe286e566993c369f3":
		return "Sonhar"
	case "67fd6407fa56fc1ddbf88602":
		return "Orçar"
	case "67fd640f554f6eea40023cc3":
		return "Poupar"
	default:
		return "Outros"
	}
}

// getLatestStatusForMonth finds the most recent financial profile status for a user up to a specific date
func (s *service) getLatestStatusForMonth(histories []*model.FinancialProfileHistory, userID string, endDate time.Time) string {
	var latestHistory *model.FinancialProfileHistory

	for _, history := range histories {
		if history.UserID == userID && history.CreatedAt.Before(endDate.Add(time.Second)) {
			if latestHistory == nil || history.CreatedAt.After(latestHistory.CreatedAt) {
				latestHistory = history
			}
		}
	}

	if latestHistory == nil {
		return ""
	}

	return latestHistory.String()
}
